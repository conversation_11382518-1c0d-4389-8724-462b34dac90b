<?php

namespace Botble\Paygate\Providers;

use Bo<PERSON>ble\Paygate\Forms\PaygatePaymentMethodForm;
use Bo<PERSON>ble\Paygate\Services\Gateways\PaygatePaymentService;
use Botble\Payment\Enums\PaymentMethodEnum;
use Botble\Payment\Facades\PaymentMethods;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerPaygateMethod'], 16, 2);
        add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithPaygate'], 16, 2);

        add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 96);

        add_filter(BASE_FILTER_ENUM_ARRAY, function ($values, $class) {
            if ($class == PaymentMethodEnum::class) {
                $values['PAYGATE'] = PAYGATE_PAYMENT_METHOD_NAME;
            }

            return $values;
        }, 21, 2);

        add_filter(BASE_FILTER_ENUM_LABEL, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == PAYGATE_PAYMENT_METHOD_NAME) {
                $value = 'Paygate';
            }

            return $value;
        }, 21, 2);

        add_filter(BASE_FILTER_ENUM_HTML, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == PAYGATE_PAYMENT_METHOD_NAME) {
                $value = PaymentMethods::getPaymentMethodImage(PAYGATE_PAYMENT_METHOD_NAME);
            }

            return $value;
        }, 21, 2);
    }

    public function addPaymentSettings(?string $settings): string
    {
        return $settings . PaygatePaymentMethodForm::create()->renderForm();
    }

    public function registerPaygateMethod(?string $html, array $data): ?string
    {
        PaymentMethods::method(PAYGATE_PAYMENT_METHOD_NAME, [
            'html' => view('plugins/paygate::methods', $data)->render(),
        ]);

        return $html;
    }

    public function checkoutWithPaygate(array $data, Request $request): array
    {
        if ($data['type'] !== PAYGATE_PAYMENT_METHOD_NAME) {
            return $data;
        }

        $paygatePaymentService = new PaygatePaymentService();

        $paymentData = apply_filters(PAYMENT_FILTER_PAYMENT_DATA, [], $request);

        if (empty($paymentData)) {
            $data['error'] = true;
            $data['message'] = __('Payment data is invalid!');

            return $data;
        }

        // Execute the payment
        $result = $paygatePaymentService->execute($paymentData);

        if ($paygatePaymentService->getErrorMessage()) {
            $data['error'] = true;
            $data['message'] = $paygatePaymentService->getErrorMessage();
        } elseif ($result) {
            // If result is a URL, redirect to Paygate payment page
            if (filter_var($result, FILTER_VALIDATE_URL)) {
                $data['checkoutUrl'] = $result;
                $data['charge_id'] = $paygatePaymentService->getChargeId();
            } else {
                $data['charge_id'] = $result;
            }

            // Call after payment hook
            $paygatePaymentService->afterMakePayment($paymentData);
        } else {
            $data['error'] = true;
            $data['message'] = __('Payment failed! Please try again.');
        }

        return $data;
    }
}
