<?php

namespace Shaqi\AdvancedOptimize\Http\Middleware;

use DOMDocument;
use DOMXPath;
use Bo<PERSON>ble\Optimize\Http\Middleware\PageSpeed;

class LazyLoad extends PageSpeed
{
    public function apply(string $buffer): string
    {
        libxml_use_internal_errors(true);

        // Create a new DOMDocument
        $dom = new DOMDocument();
        // Load the HTML content
        $dom->loadHTML($buffer, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $xpath = new DOMXPath($dom);

        // Use XPath query to find img tags with missing or blank alt attributes
        $imgTags = $xpath->query('//img[not(@loading) or @loading=""]');

        // Process the results
        if ($imgTags->length > 0) {
            foreach ($imgTags as $imgTag) {

                // Check if the img has the data-pagepeed-no-lazy attribute
                if ($imgTag->hasAttribute('data-pagepeed-no-lazy')) {
                    // Skip the img with data-skip-preload
                    continue;
                }
                // Set the loading="lazy" attribute to the img tag
                $imgTag->setAttribute('loading', 'lazy');
            }
        }

        return $dom->saveHTML();
    }
}
