<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Listeners;

use Bo<PERSON>ble\Base\Events\RenderingAdminWidgetEvent;
use <PERSON><PERSON>ble\ExpenseCalculations\Widgets\ExpenseCalculationsWidget;
use <PERSON><PERSON>ble\ExpenseCalculations\Widgets\FinancialReportsChart;
use Botble\ExpenseCalculations\Widgets\NetProfitCard;
use Botble\ExpenseCalculations\Widgets\TotalExpensesCard;
use Botble\ExpenseCalculations\Widgets\TotalRevenueCard;
use Bo<PERSON>ble\ExpenseCalculations\Widgets\TotalSalesCard;

class RegisterExpenseCalculationsWidget
{
    public function handle(RenderingAdminWidgetEvent $event): void
    {
        $event->widget
            ->register([
                TotalSalesCard::class,
                TotalRevenueCard::class,
                TotalExpensesCard::class,
                NetProfitCard::class,
                FinancialReportsChart::class,
                ExpenseCalculationsWidget::class,
            ], 'expense-calculations');
    }
}
