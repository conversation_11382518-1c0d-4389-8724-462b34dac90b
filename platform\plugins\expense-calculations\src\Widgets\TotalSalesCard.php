<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Widgets;

use Bo<PERSON>ble\Base\Widgets\Card;
use <PERSON><PERSON>ble\ExpenseCalculations\Services\ExpenseCalculationService;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Illuminate\Http\Request;

class TotalSalesCard extends Card
{
    protected ExpenseCalculationService $expenseCalculationService;

    public function __construct()
    {
        $this->expenseCalculationService = app('expense-calculation-service');
    }

    public function getOptions(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $totalSales = $this->expenseCalculationService->getTotalSalesCount($startDate, $endDate);

        return [
            'series' => [
                [
                    'data' => [$totalSales],
                ],
            ],
        ];
    }

    public function getViewData(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $totalSales = $this->expenseCalculationService->getTotalSalesCount($startDate, $endDate);
        $salesData = $this->expenseCalculationService->getDailySalesData($startDate, $endDate);

        // Ensure we have valid data for the chart
        $chartData = [];
        $chartDates = [];

        if (!empty($salesData)) {
            // Sort by date to ensure proper order
            ksort($salesData);

            foreach ($salesData as $date => $value) {
                $chartDates[] = $date;
                $chartData[] = is_numeric($value) ? (float) $value : 0;
            }
        } else {
            // If no data, create a single point with zero value for the date range
            $dateStr = $startDate ? ($startDate instanceof \DateTime ? $startDate->format('Y-m-d') : $startDate) : date('Y-m-d');
            $chartDates[] = $dateStr;
            $chartData[] = 0;
        }

        return array_merge(parent::getViewData(), [
            'content' => view(
                'plugins/expense-calculations::reports.widgets.total-sales-card',
                [
                    'totalSales' => $totalSales,
                    'result' => 0, // You can calculate percentage change here
                    'chartData' => $chartData,
                    'chartDates' => $chartDates,
                ]
            )->render(),
        ]);
    }
}
