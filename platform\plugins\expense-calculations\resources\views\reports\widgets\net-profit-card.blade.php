<x-core::card class="analytic-card">
    <x-core::card.body class="p-3">
        <div class="row align-items-center">
            <div class="col-auto">
                <x-core::icon
                    class="text-white {{ $netProfit >= 0 ? 'bg-green' : 'bg-red' }} rounded p-1"
                    name="ti ti-calculator"
                    size="md"
                />
            </div>
            <div class="col mt-0">
                <p class="text-secondary mb-0 fs-4">
                    {{ trans('plugins/expense-calculations::expense-calculations.net_profit') }}
                </p>
                <h3 class="mb-n1 fs-1 {{ $netProfit >= 0 ? 'text-success' : 'text-danger' }}">
                    {{ format_price($netProfit) }}
                </h3>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <div class="rp-card-chart position-relative">
                    <div id="net-profit-chart"></div>
                </div>
            </div>
        </div>
    </x-core::card.body>
    @include('plugins/expense-calculations::reports.widgets.card-description')
</x-core::card>

<script>
    $(document).ready(function() {
        if (document.querySelector('#net-profit-chart')) {
            new ApexCharts(document.querySelector('#net-profit-chart'), {
                series: [{
                    name: '{{ trans('plugins/expense-calculations::expense-calculations.net_profit') }}',
                    data: @json($chartData)
                }],
                chart: {
                    height: 100,
                    type: 'area',
                    toolbar: { show: false },
                    sparkline: { enabled: true }
                },
                dataLabels: { enabled: false },
                stroke: {
                    curve: 'smooth',
                    width: 2
                },
                colors: ['{{ $netProfit >= 0 ? '#28a745' : '#dc3545' }}'],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.1,
                    }
                },
                xaxis: {
                    type: 'datetime',
                    categories: @json($chartDates),
                    labels: { show: false },
                    axisBorder: { show: false },
                    axisTicks: { show: false }
                },
                yaxis: { show: false },
                grid: { show: false },
                tooltip: {
                    x: { format: 'dd/MM/yy' },
                    y: {
                        formatter: function(value) {
                            return new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: 'USD'
                            }).format(value);
                        }
                    }
                }
            }).render();
        }
    });
</script>
