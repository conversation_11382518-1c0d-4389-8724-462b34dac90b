<?php

namespace Botble\ExpenseCalculations\Repositories\Interfaces;

use Botble\Support\Repositories\Interfaces\RepositoryInterface;

interface ExpenseInterface extends RepositoryInterface
{
    public function getTotalExpenses($startDate = null, $endDate = null);
    
    public function getExpensesByCategory($startDate = null, $endDate = null);
    
    public function getExpensesByDateRange($startDate, $endDate);
}
