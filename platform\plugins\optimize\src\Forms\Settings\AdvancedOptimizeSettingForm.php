<?php

namespace Shaqi\AdvancedOptimize\Forms\Settings;

use <PERSON>haqi\AdvancedOptimize\Http\Requests\AdvancedOptimizeSettingRequest;
use Bo<PERSON>ble\Setting\Forms\SettingForm;

class AdvancedOptimizeSettingForm extends SettingForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->setSectionTitle(trans('plugins/optimize::optimize.settings.title'))
            ->setSectionDescription(trans('plugins/optimize::optimize.settings.description'))
            ->setValidatorClass(AdvancedOptimizeSettingRequest::class)
            ->add('optimize_fields', 'html', [
                'html' => view('plugins/optimize::partials.settings.forms.optimize-fields')->render(),
            ]);
    }
}
