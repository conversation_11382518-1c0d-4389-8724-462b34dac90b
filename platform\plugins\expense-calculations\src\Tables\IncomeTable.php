<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Tables;

use Botble\ExpenseCalculations\Models\Income;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Bo<PERSON>ble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\FormattedColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class IncomeTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Income::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('expense-calculations.income.create'))
            ->addColumns([
                IdColumn::make(),
                NameColumn::make('title')->route('expense-calculations.income.edit'),
                FormattedColumn::make('amount')
                    ->title(trans('plugins/expense-calculations::income.amount'))
                    ->alignEnd()
                    ->renderUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();
                        return format_price($item->amount);
                        // return $item->amount_format;
                    }),
                Column::make('source')
                    ->title(trans('plugins/expense-calculations::income.source')),
                FormattedColumn::make('income_date')
                    ->title(trans('plugins/expense-calculations::income.income_date'))
                    ->renderUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();
                        return $item->income_date->format('Y-m-d');
                    }),
                StatusColumn::make(),
                CreatedAtColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('expense-calculations.income.edit'),
                DeleteAction::make()->route('expense-calculations.income.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('expense-calculations.income.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'title',
                        'amount',
                        'source',
                        'income_date',
                        'status',
                        'created_at',
                    ]);
            });
    }
}
