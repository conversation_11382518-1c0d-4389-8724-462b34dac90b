<?php

namespace Shaqi\AdvancedOptimize\Http\Controllers\Settings;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Shaqi\AdvancedOptimize\Forms\Settings\AdvancedOptimizeSettingForm;
use <PERSON><PERSON>qi\AdvancedOptimize\Http\Requests\AdvancedOptimizeSettingRequest;
use Botble\Setting\Http\Controllers\SettingController;

class AdvancedOptimizeSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('plugins/optimize::optimize.settings.title'));

        return AdvancedOptimizeSettingForm::create()->renderForm();
    }

    public function update(AdvancedOptimizeSettingRequest $request): BaseHttpResponse
    {
        return $this->performUpdate($request->validated());
    }
}
