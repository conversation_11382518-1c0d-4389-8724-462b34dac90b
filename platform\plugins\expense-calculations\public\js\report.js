// Expense Calculations Report functionality - similar to ecommerce reports
$(() => {
    console.log('Expense calculations report.js loaded')
    console.log('window.moment:', typeof window.moment)
    console.log('jQuery().daterangepicker:', typeof jQuery().daterangepicker)

    // Add a small delay to ensure all dependencies are loaded
    setTimeout(() => {
        initializeDateRangePicker()
    }, 100)
})

function initializeDateRangePicker() {
    console.log('Initializing date range picker...')
    console.log('window.moment:', typeof window.moment)
    console.log('jQuery().daterangepicker:', typeof jQuery().daterangepicker)

    if (!window.moment) {
        console.error('Moment.js is not loaded')
        return
    }

    if (!jQuery().daterangepicker) {
        console.error('DateRangePicker plugin is not loaded')
        return
    }

    moment.locale($('html').attr('lang'))

    let $dateRange = $(document).find('.date-range-picker')
    console.log('Found date range picker elements:', $dateRange.length)
    console.log('Date range picker element:', $dateRange[0])

    if (!$dateRange.length) {
        console.warn('No date range picker elements found')
        return
    }

    let dateFormat = $dateRange.data('format') || 'YYYY-MM-DD'
    let startDate = $dateRange.data('start-date') ? moment($dateRange.data('start-date')) : moment().subtract(29, 'days')
    let endDate = $dateRange.data('end-date') ? moment($dateRange.data('end-date')) : moment()

    let today = moment()
    if (endDate > today) {
        endDate = today
    }

    if (!window.BotbleVariables || !window.BotbleVariables.languages || !window.BotbleVariables.languages.reports) {
        console.error('BotbleVariables.languages.reports is not available')
        return
    }

    let rangesTrans = BotbleVariables.languages.reports

    let ranges = {
        [rangesTrans.today]: [today, today],
        [rangesTrans.this_week]: [moment().startOf('week'), today],
        [rangesTrans.last_7_days]: [moment().subtract(6, 'days'), today],
        [rangesTrans.last_30_days]: [moment().subtract(29, 'days'), today],
        [rangesTrans.this_month]: [moment().startOf('month'), endDate],
        [rangesTrans.this_year]: [moment().startOf('year'), moment().endOf('year')],
    }

    console.log('About to initialize daterangepicker with config:', {
        ranges: Object.keys(ranges),
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
        dateFormat: dateFormat
    })

    try {
        $dateRange.daterangepicker(
            {
                ranges: ranges,
                alwaysShowCalendars: true,
                startDate: startDate,
                endDate: endDate,
                maxDate: today, // Use today instead of endDate for maxDate
                opens: 'left',
                drops: 'auto',
                locale: {
                    format: dateFormat,
                },
                autoUpdateInput: false,
            },
        function (start, end, label) {
            $.ajax({
                url: $dateRange.data('href'),
                data: {
                    date_from: start.format('YYYY-MM-DD'),
                    date_to: end.format('YYYY-MM-DD'),
                    predefined_range: label,
                },
                type: 'GET',
                success: (data) => {
                    if (data.error) {
                        Botble.showError(data.message)
                    } else {
                        if (!$('#report-stats-content').length) {
                            const newUrl = new URL(window.location.href)

                            newUrl.searchParams.set('date_from', start.format('YYYY-MM-DD'))
                            newUrl.searchParams.set('date_to', end.format('YYYY-MM-DD'))

                            history.pushState({ urlPath: newUrl.href }, '', newUrl.href)

                            window.location.reload()
                        } else {
                            // Update the report content
                            $('#report-stats-content').html(data.data)

                            // Re-initialize any widgets or charts if needed
                            if (typeof window.initializeCharts === 'function') {
                                window.initializeCharts()
                            }
                        }

                        // Update button text
                        let formatValue = $dateRange.data('format-value')
                        if (formatValue) {
                            let newText = formatValue
                                .replace('__from__', start.format(dateFormat))
                                .replace('__to__', end.format(dateFormat))

                            // Check if there's a span inside the button, if so update it, otherwise update the button content
                            if ($dateRange.find('span').length) {
                                $dateRange.find('span').text(newText)
                            } else {
                                $dateRange.html(newText)
                            }
                        }
                    }
                },
                error: (xhr, status, error) => {
                    console.error('Date range picker error:', error)
                    Botble.showError('Something went wrong!')
                }
            })
        }
    )

    console.log('DateRangePicker initialized successfully')

    } catch (error) {
        console.error('Error initializing daterangepicker:', error)
        return
    }

    $dateRange.on('apply.daterangepicker', function (ev, picker) {
        let $this = $(this)
        let formatValue = $this.data('format-value')
        if (formatValue) {
            let value = formatValue
                .replace('__from__', picker.startDate.format(dateFormat))
                .replace('__to__', picker.endDate.format(dateFormat))

            // Check if there's a span inside the button, if so update it, otherwise update the button content
            if ($this.find('span').length) {
                $this.find('span').text(value)
            } else {
                $this.html(value)
            }
        }
    })

    $dateRange.on('cancel.daterangepicker', function (ev, picker) {
        // Reset to original values if cancelled
        let $this = $(this)
        let originalStartDate = $this.data('start-date')
        let originalEndDate = $this.data('end-date')
        let formatValue = $this.data('format-value')

        if (formatValue && originalStartDate && originalEndDate) {
            let value = formatValue
                .replace('__from__', moment(originalStartDate).format(dateFormat))
                .replace('__to__', moment(originalEndDate).format(dateFormat))

            // Check if there's a span inside the button, if so update it, otherwise update the button content
            if ($this.find('span').length) {
                $this.find('span').text(value)
            } else {
                $this.html(value)
            }
        }
    })
}
