<?php

namespace Shaqi\AdvancedOptimize\Http\Requests;

use Botble\Base\Rules\OnOffRule;
use Botble\Support\Http\Requests\Request;

class AdvancedOptimizeSettingRequest extends Request
{
    public function rules(): array
    {
        return [
            'optimize_page_speed_enable' => $onOffRule = new OnOffRule(),
            'optimize_preload_style' => $onOffRule,
            'optimize_alt_attribute' => $onOffRule,
            'optimize_lazy_loading' => $onOffRule,
            'optimize_reduce_dom_size' => $onOffRule,
            'optimize_trim_url' => $onOffRule,
            'optimize_reduce_redirects' => $onOffRule,
        ];
    }
}
