<?php

namespace Botble\ExpenseCalculations\Repositories\Eloquent;

use Botble\ExpenseCalculations\Repositories\Interfaces\ExpenseInterface;
use Bo<PERSON>ble\Support\Repositories\Eloquent\RepositoriesAbstract;

class ExpenseRepository extends RepositoriesAbstract implements ExpenseInterface
{
    public function getTotalExpenses($startDate = null, $endDate = null)
    {
        return $this->model->getTotalExpenses($startDate, $endDate);
    }
    
    public function getExpensesByCategory($startDate = null, $endDate = null)
    {
        return $this->model->getExpensesByCategory($startDate, $endDate);
    }
    
    public function getExpensesByDateRange($startDate, $endDate)
    {
        return $this->model->active()->byDateRange($startDate, $endDate)->get();
    }
}
