/* Report page styles */
.report-chart-content {
    margin-bottom: 20px;
}

.rp-card-chart {
    position: relative;
    height: 200px;
}

.rp-card-information {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
}

.rp-card-information strong {
    font-size: 1.5rem;
    display: block;
    margin-bottom: 5px;
}

.rp-card-information small {
    font-size: 0.875rem;
    opacity: 0.8;
}

.rp-card-status p {
    margin-bottom: 8px;
    font-size: 0.875rem;
}

/* Date range picker styles */
.date-range-picker {
    border: 1px solid #dee2e6;
    background: #fff;
    color: #495057;
}

.date-range-picker:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

/* Widget loading styles */
.widget-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 100;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .rp-card-information strong {
        font-size: 1.25rem;
    }

    .rp-card-status {
        font-size: 0.8rem;
    }
}
