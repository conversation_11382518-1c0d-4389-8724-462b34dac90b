<?php

use Botble\Base\Facades\AdminHelper;
use Illuminate\Support\Facades\Route;


Route::group(['namespace' => 'Shaqi\AdvancedOptimize\Http\Controllers'], function () {
    AdminHelper::registerRoutes(function () {
        Route::group(['prefix' => 'settings'], function () {
            Route::get('optimize/advanced', [
                'as' => 'advanced.optimize.settings',
                'uses' => 'Settings\AdvancedOptimizeSettingController@edit',
            ]);

            Route::put('optimize/advanced', [
                'as' => 'advanced.optimize.settings.update',
                'uses' => 'Settings\AdvancedOptimizeSettingController@update',
                'permission' => 'optimize.settings',
            ]);
        });
    });
});
