<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Http\Controllers;

use Botble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Botble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Botble\Base\Forms\FormBuilder;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\ExpenseCalculations\Forms\ExpenseForm;
use Botble\ExpenseCalculations\Http\Requests\ExpenseRequest;

use Botble\ExpenseCalculations\Repositories\Interfaces\ExpenseInterface;
use Botble\ExpenseCalculations\Tables\ExpenseTable;
use Exception;
use Illuminate\Http\Request;

class ExpenseController extends BaseController
{
    public function __construct(protected ExpenseInterface $expenseRepository)
    {
    }

    public function index(ExpenseTable $table)
    {
        PageTitle::setTitle(trans('plugins/expense-calculations::expenses.name'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/expense-calculations::expenses.create'));

        return $formBuilder->create(ExpenseForm::class)->renderForm();
    }

    public function store(ExpenseRequest $request, BaseHttpResponse $response)
    {
        try {
            $expense = $this->expenseRepository->createOrUpdate($request->validated());

            event(new CreatedContentEvent(EXPENSE_MODULE_SCREEN_NAME, $request, $expense));

            return $response
                ->setPreviousUrl(route('expense-calculations.expenses.index'))
                ->setMessage(trans('core/base::notices.create_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function show(int|string $id, Request $request)
    {
        $expense = $this->expenseRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $expense));

        PageTitle::setTitle(trans('plugins/expense-calculations::expenses.edit') . ' "' . $expense->title . '"');

        return view('plugins/expense-calculations::expenses.show', compact('expense'));
    }

    public function edit(int|string $id, FormBuilder $formBuilder, Request $request)
    {
        $expense = $this->expenseRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $expense));

        PageTitle::setTitle(trans('plugins/expense-calculations::expenses.edit') . ' "' . $expense->title . '"');

        return $formBuilder->create(ExpenseForm::class, ['model' => $expense])->renderForm();
    }

    public function update(int|string $id, ExpenseRequest $request, BaseHttpResponse $response)
    {
        $expense = $this->expenseRepository->findOrFail($id);

        $expense->fill($request->validated());

        $expense = $this->expenseRepository->createOrUpdate($expense);

        event(new UpdatedContentEvent(EXPENSE_MODULE_SCREEN_NAME, $request, $expense));

        return $response
            ->setPreviousUrl(route('expense-calculations.expenses.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(int|string $id, Request $request, BaseHttpResponse $response)
    {
        try {
            $expense = $this->expenseRepository->findOrFail($id);

            $this->expenseRepository->delete($expense);

            event(new DeletedContentEvent(EXPENSE_MODULE_SCREEN_NAME, $request, $expense));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }
}
