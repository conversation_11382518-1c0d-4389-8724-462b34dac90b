@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <x-core::card>
                <x-core::card.header>
                    <x-core::card.title>
                        {{ trans('plugins/expense-calculations::income.edit') }}: {{ $income->title }}
                    </x-core::card.title>
                    <div class="card-actions">
                        <a href="{{ route('expense-calculations.income.edit', $income->id) }}" class="btn btn-primary">
                            <x-core::icon name="ti ti-edit" />
                            {{ trans('core/base::forms.edit') }}
                        </a>
                        <a href="{{ route('expense-calculations.income.index') }}" class="btn btn-secondary">
                            <x-core::icon name="ti ti-arrow-left" />
                            {{ trans('core/base::forms.back') }}
                        </a>
                    </div>
                </x-core::card.header>
                <x-core::card.body>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::income.title') }}</label>
                                <p class="form-control-plaintext">{{ $income->title }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::income.amount') }}</label>
                                <p class="form-control-plaintext">{{ $income->amount_format }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::income.source') }}</label>
                                <p class="form-control-plaintext">{{ $income->source ?: '—' }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::income.income_date') }}</label>
                                <p class="form-control-plaintext">{{ $income->income_date->format('Y-m-d') }}</p>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::income.reference_number') }}</label>
                                <p class="form-control-plaintext">{{ $income->reference_number ?: '—' }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('core/base::tables.status') }}</label>
                                <p class="form-control-plaintext">
                                    <x-core::badge :type="$income->status->toHtml()">
                                        {{ $income->status->label() }}
                                    </x-core::badge>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('core/base::tables.created_at') }}</label>
                                <p class="form-control-plaintext">{{ $income->created_at->format('Y-m-d H:i:s') }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('core/base::tables.updated_at') }}</label>
                                <p class="form-control-plaintext">{{ $income->updated_at->format('Y-m-d H:i:s') }}</p>
                            </div>
                        </div>
                        
                        @if($income->description)
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::income.description') }}</label>
                                <div class="form-control-plaintext">{{ $income->description }}</div>
                            </div>
                        </div>
                        @endif
                    </div>
                </x-core::card.body>
            </x-core::card>
        </div>
    </div>
@endsection
