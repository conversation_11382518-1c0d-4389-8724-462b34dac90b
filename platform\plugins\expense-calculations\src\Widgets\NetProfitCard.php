<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Widgets;

use Bo<PERSON>ble\Base\Widgets\Card;
use <PERSON><PERSON>ble\ExpenseCalculations\Services\ExpenseCalculationService;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Illuminate\Http\Request;

class NetProfitCard extends Card
{
    protected ExpenseCalculationService $expenseCalculationService;

    public function __construct()
    {
        $this->expenseCalculationService = app('expense-calculation-service');
    }

    public function getOptions(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $netProfit = $this->expenseCalculationService->getNetProfit($startDate, $endDate);

        return [
            'series' => [
                [
                    'data' => [$netProfit],
                ],
            ],
        ];
    }

    public function getViewData(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $netProfit = $this->expenseCalculationService->getNetProfit($startDate, $endDate);

        // Calculate daily profit (revenue - expenses)
        $revenueData = $this->expenseCalculationService->getDailyRevenueData($startDate, $endDate);
        $expenseData = $this->expenseCalculationService->getDailyExpenseData($startDate, $endDate);

        $profitData = [];
        $allDates = array_unique(array_merge(array_keys($revenueData), array_keys($expenseData)));

        foreach ($allDates as $date) {
            $profitData[$date] = ($revenueData[$date] ?? 0) - ($expenseData[$date] ?? 0);
        }
        ksort($profitData);

        return array_merge(parent::getViewData(), [
            'content' => view(
                'plugins/expense-calculations::reports.widgets.net-profit-card',
                [
                    'netProfit' => $netProfit,
                    'result' => 0, // You can calculate percentage change here
                    'chartData' => array_values($profitData),
                    'chartDates' => array_keys($profitData),
                ]
            )->render(),
        ]);
    }
}
