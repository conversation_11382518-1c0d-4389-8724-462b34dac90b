@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <x-core::card>
                <x-core::card.header>
                    <x-core::card.title>
                        {{ trans('plugins/expense-calculations::expenses.edit') }}: {{ $expense->title }}
                    </x-core::card.title>
                    <div class="card-actions">
                        <a href="{{ route('expense-calculations.expenses.edit', $expense->id) }}" class="btn btn-primary">
                            <x-core::icon name="ti ti-edit" />
                            {{ trans('core/base::forms.edit') }}
                        </a>
                        <a href="{{ route('expense-calculations.expenses.index') }}" class="btn btn-secondary">
                            <x-core::icon name="ti ti-arrow-left" />
                            {{ trans('core/base::forms.back') }}
                        </a>
                    </div>
                </x-core::card.header>
                <x-core::card.body>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::expenses.title') }}</label>
                                <p class="form-control-plaintext">{{ $expense->title }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::expenses.amount') }}</label>
                                <p class="form-control-plaintext">{{ $expense->amount_format }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::expenses.category') }}</label>
                                <p class="form-control-plaintext">{{ $expense->category ?: '—' }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::expenses.expense_date') }}</label>
                                <p class="form-control-plaintext">{{ $expense->expense_date->format('Y-m-d') }}</p>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::expenses.reference_number') }}</label>
                                <p class="form-control-plaintext">{{ $expense->reference_number ?: '—' }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('core/base::tables.status') }}</label>
                                <p class="form-control-plaintext">
                                    <x-core::badge :type="$expense->status->toHtml()">
                                        {{ $expense->status->label() }}
                                    </x-core::badge>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('core/base::tables.created_at') }}</label>
                                <p class="form-control-plaintext">{{ $expense->created_at->format('Y-m-d H:i:s') }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ trans('core/base::tables.updated_at') }}</label>
                                <p class="form-control-plaintext">{{ $expense->updated_at->format('Y-m-d H:i:s') }}</p>
                            </div>
                        </div>
                        
                        @if($expense->description)
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">{{ trans('plugins/expense-calculations::expenses.description') }}</label>
                                <div class="form-control-plaintext">{{ $expense->description }}</div>
                            </div>
                        </div>
                        @endif
                    </div>
                </x-core::card.body>
            </x-core::card>
        </div>
    </div>
@endsection
