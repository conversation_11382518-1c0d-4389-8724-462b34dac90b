<?php

namespace Botble\Paygate\Services\Gateways;

use Botble\Paygate\Services\Abstracts\PaygatePaymentAbstract;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class PaygatePaymentService extends PaygatePaymentAbstract
{
    protected string $baseUrl = 'https://api.paygate.to';
    protected string $checkoutUrl = 'https://checkout.paygate.to';

    public function makePayment(array $data): bool|string
    {
        try {
            // Validate required data
            if (empty($this->walletAddress)) {
                $this->setErrorMessage('Paygate wallet address is not configured');
                return false;
            }

            // Get customer email from data - try multiple possible keys
            $customerEmail = $data['customer_email'] ??
                           $data['email'] ??
                           $data['address']['email'] ??
                           $data['billing_address']['email'] ??
                           '<EMAIL>';

            // Step 1: Create Payment Address
            $paymentAddress = $this->createPaymentAddress($data['order_id'][0] ?? null);

            if (!$paymentAddress) {
                return false;
            }

            // Step 2: Convert Currency to USD
            $convertedAmount = $this->convertCurrency();
            if (!$convertedAmount) {
                return false;
            }

            // Step 3: Generate Checkout URL
            $checkoutUrl = $this->generateCheckoutUrl($paymentAddress, $convertedAmount, $customerEmail);
            if (!$checkoutUrl) {
                return false;
            }

            // Set charge ID for tracking
            $this->chargeId = $paymentAddress['ipn_token'] ?? Str::random(20);

            do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
                'amount' => $convertedAmount,
                'currency' => $this->currency,
                'charge_id' => $this->chargeId,
                'order_id' => $data['order_id'][0] ?? null,
                'payment_channel' => PAYGATE_PAYMENT_METHOD_NAME,
                'status' => PaymentStatusEnum::PENDING,
            ]);

            return $checkoutUrl;

        } catch (Exception $exception) {
            $this->setErrorMessageAndLogging($exception, 2);
            return false;
        }
    }

    protected function createPaymentAddress($orderId = null): array|false
    {
        try {
            $callbackUrl = route('paygate.webhook', [
                'order_id' => $orderId
            ]);

            $apiUrl = sprintf(
                '%s/control/wallet.php?address=%s&callback=%s',
                $this->baseUrl,
                urlencode($this->walletAddress),
                urlencode($callbackUrl)
            );

            $requestData = [
                'address' => $this->walletAddress,
                'callback' => $callbackUrl,
            ];

            do_action('payment_before_making_api_request', PAYGATE_PAYMENT_METHOD_NAME, $requestData);

            $response = Http::timeout(30)->withHeaders([
                'Accept' => 'application/json',
                'User-Agent' => 'Botble-Paygate-Plugin/1.0.0',
            ])->get($apiUrl);

            $responseData = $response->json() ?? [];

            do_action('payment_after_api_response', PAYGATE_PAYMENT_METHOD_NAME, $requestData, $responseData);

            if ($response->successful() && isset($responseData['address_in'])) {
                PaymentHelper::log(
                    PAYGATE_PAYMENT_METHOD_NAME,
                    [
                        'message' => 'Payment address created successfully',
                        'callback_url' => $responseData['callback_url'],
                        'polygon_address_in' => $responseData['polygon_address_in'],
                        'address_in' => $responseData['address_in'],
                        'ipn_token' => $responseData['ipn_token'],
                    ]
                );
                return $responseData;
            }

            $this->setErrorMessage('Failed to create payment address: ' . ($responseData['error'] ?? 'Unknown error'));
            return false;

        } catch (Exception $exception) {
            $this->setErrorMessage('Error creating payment address: ' . $exception->getMessage());
            return false;
        }
    }

    protected function convertCurrency(): string|false
    {
        try {
            $apiUrl = sprintf(
                '%s/control/convert.php?from=%s&value=%.2f',
                $this->baseUrl,
                $this->currency,
                $this->amount
            );

            $requestData = [
                'from' => $this->currency,
                'value' => $this->amount,
            ];

            do_action('payment_before_making_api_request', PAYGATE_PAYMENT_METHOD_NAME, $requestData);

            $response = Http::timeout(30)->withHeaders([
                'Accept' => 'application/json',
                'User-Agent' => 'Botble-Paygate-Plugin/1.0.0',
            ])->get($apiUrl);

            $responseData = $response->json() ?? [];

            do_action('payment_after_api_response', PAYGATE_PAYMENT_METHOD_NAME, $requestData, $responseData);

            if ($response->successful() &&
                isset($responseData['status']) &&
                $responseData['status'] === 'success' &&
                isset($responseData['value_coin'])) {
                return $responseData['value_coin'];
            }

            $this->setErrorMessage('Failed to convert currency: ' . ($responseData['error'] ?? 'Unknown error'));
            return false;

        } catch (Exception $exception) {
            $this->setErrorMessage('Error converting currency: ' . $exception->getMessage());
            return false;
        }
    }

    protected function generateCheckoutUrl(array $paymentAddress, string $convertedAmount, string $customerEmail): string|false
    {
        try {
            $checkoutUrl = sprintf(
                '%s/pay.php?address=%s&amount=%s&email=%s&currency=USD&domain=checkout.paygate.to',
                $this->checkoutUrl,
                $paymentAddress['address_in'],
                $convertedAmount,
                urlencode($customerEmail)
            );
            return $checkoutUrl;

        } catch (Exception $exception) {
            $this->setErrorMessage('Error generating checkout URL: ' . $exception->getMessage());
            return false;
        }
    }

    public function afterMakePayment(array $data): void
    {
        PaymentHelper::log(
            PAYGATE_PAYMENT_METHOD_NAME,
            [
                'message' => 'Payment link created successfully',
                'charge_id' => $this->chargeId,
                'amount' => $this->amount,
                'currency' => $this->currency,
            ]
        );
    }
}
