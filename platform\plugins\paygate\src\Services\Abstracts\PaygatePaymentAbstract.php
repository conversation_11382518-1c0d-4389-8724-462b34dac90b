<?php

namespace Botble\Paygate\Services\Abstracts;

use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Exception;

abstract class PaygatePaymentAbstract
{
    protected string $walletAddress;
    protected float $amount;
    protected string $currency;
    protected string $chargeId;
    protected string $errorMessage = '';

    public function __construct()
    {
        $this->walletAddress = get_payment_setting('wallet_address', PAYGATE_PAYMENT_METHOD_NAME);
    }

    public function getWalletAddress(): string
    {
        return $this->walletAddress;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function getChargeId(): string
    {
        return $this->chargeId;
    }

    public function getErrorMessage(): string
    {
        return $this->errorMessage;
    }

    public function setErrorMessage(string $errorMessage): void
    {
        $this->errorMessage = $errorMessage;
    }

    protected function setErrorMessageAndLogging(Exception $exception, int $line = 0): void
    {
        $this->errorMessage = $exception->getMessage();

        PaymentHelper::log(
            PAYGATE_PAYMENT_METHOD_NAME,
            [
                'error' => $this->errorMessage,
                'line' => $line,
                'file' => $exception->getFile(),
                'trace' => $exception->getTraceAsString(),
            ]
        );
    }

    public function execute(array $data): bool|string
    {
        try {
            $this->amount = $data['amount'];
            $this->currency = $data['currency'];

            return $this->makePayment($data);
        } catch (Exception $exception) {
            $this->setErrorMessageAndLogging($exception, 1);

            return false;
        }
    }

    abstract public function makePayment(array $data): bool|string;

    public function supportedCurrencyCodes(): array
    {
        return [
            'USD',
            'EUR',
            'GBP',
            'CAD',
            'AUD',
            'JPY',
            'CHF',
            'CNY',
            'SEK',
            'NZD',
        ];
    }

    abstract public function afterMakePayment(array $data): void;
}
