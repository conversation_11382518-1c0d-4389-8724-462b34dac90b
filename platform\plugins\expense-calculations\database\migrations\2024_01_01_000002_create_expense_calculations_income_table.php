<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('ec_income', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->decimal('amount', 15, 2);
            $table->string('source')->nullable();
            $table->date('income_date');
            $table->string('reference_number')->nullable();
            $table->json('attachments')->nullable();
            $table->string('status', 60)->default('active');
            $table->timestamps();
            
            $table->index(['income_date', 'status']);
            $table->index('source');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ec_income');
    }
};
