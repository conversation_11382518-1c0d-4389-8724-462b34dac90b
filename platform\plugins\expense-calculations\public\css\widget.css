/* Widget styles similar to ecommerce plugin */
.expense-calculations-status-list {
    margin: 0;
    padding: 0;
}

.expense-calculations-status-list li {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.expense-calculations-status-list li:last-child {
    border-bottom: none;
}

.expense-calculations-status-list li:hover {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding-left: 8px;
    padding-right: 8px;
}

.expense-calculations-status-list li .ti {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    opacity: 0.7;
}

.expense-calculations-status-list li.total-sales .ti {
    color: #007bff;
}

.expense-calculations-status-list li.top-selling-items .ti {
    color: #28a745;
}

.expense-calculations-status-list li.total-expenses .ti {
    color: #dc3545;
}

.expense-calculations-status-list li.total-revenue .ti {
    color: #17a2b8;
}

.expense-calculations-status-list li.net-profit .ti {
    color: #6f42c1;
}

.expense-calculations-status-list li a {
    color: #495057;
    text-decoration: none;
    font-size: 14px;
    line-height: 1.4;
    flex: 1;
}

.expense-calculations-status-list li a:hover {
    color: #007bff;
    text-decoration: none;
}

.expense-calculations-status-list li a strong {
    font-weight: 600;
    margin-right: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
    .expense-calculations-status-list li {
        padding: 10px 0;
    }
    
    .expense-calculations-status-list li a {
        font-size: 13px;
    }
}

/* Loading state */
.widget-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #6c757d;
}

.widget-loading .fa-spinner {
    margin-right: 8px;
}

/* Error state */
.widget-error {
    padding: 20px;
    text-align: center;
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
}

/* Success/positive values */
.text-success {
    color: #28a745 !important;
}

/* Danger/negative values */
.text-danger {
    color: #dc3545 !important;
}
