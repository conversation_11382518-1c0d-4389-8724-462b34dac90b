<?php

namespace Shaqi\AdvancedOptimize\Http\Middleware;

use Botble\Optimize\Http\Middleware\PageSpeed;
use DOMDocument;
use DOMXPath;

class AltAttribute extends PageSpeed
{
    public function apply(string $buffer): string
    {
        libxml_use_internal_errors(true);

        // Create a new DOMDocument
        $dom = new DOMDocument();
        // Load the HTML content
        $dom->loadHTML($buffer, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $xpath = new DOMXPath($dom);

        // Create a new DOMXPath object
        $xpath = new DOMXPath($dom);

        // Use XPath query to find img tags with missing or blank alt attributes
        $imgTags = $xpath->query('//img[not(@alt) or @alt=""]');

        // Process the results
        if ($imgTags->length > 0) {
            foreach ($imgTags as $imgTag) {
                // Get the image source
                $src = $imgTag->getAttribute('src');

                // Extract the file name from the image source without the file extension
                $fileName = pathinfo($src, PATHINFO_FILENAME);

                // Set the alt attribute to the file name without the extension
                $imgTag->setAttribute('alt', $fileName);
            }
        }

        return $dom->saveHTML();
    }
}
