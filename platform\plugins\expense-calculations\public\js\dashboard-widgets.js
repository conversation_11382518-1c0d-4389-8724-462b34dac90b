/**
 * Dashboard Widgets JavaScript for Expense Calculations Plugin
 */

(function($) {
    'use strict';

    class ExpenseCalculationsWidgets {
        constructor() {
            this.init();
        }

        init() {
            this.bindEvents();
            this.initializeWidgets();
        }

        bindEvents() {
            // Widget refresh functionality
            $(document).on('click', '.widget-refresh-btn', (e) => {
                e.preventDefault();
                this.refreshWidget($(e.target).closest('.widget-item'));
            });

            // Widget settings
            $(document).on('click', '.widget-settings-btn', (e) => {
                e.preventDefault();
                this.showWidgetSettings($(e.target).closest('.widget-item'));
            });
        }

        initializeWidgets() {
            // Initialize any widget-specific functionality
            this.initCounterAnimations();
            this.initTooltips();
        }

        initCounterAnimations() {
            $('.widget-counter').each(function() {
                const $counter = $(this);
                const target = parseInt($counter.data('target') || $counter.text().replace(/[^0-9]/g, ''));
                
                if (target && !isNaN(target)) {
                    $counter.prop('Counter', 0).animate({
                        Counter: target
                    }, {
                        duration: 1500,
                        easing: 'swing',
                        step: function(now) {
                            $counter.text(Math.ceil(now).toLocaleString());
                        }
                    });
                }
            });
        }

        initTooltips() {
            // Initialize tooltips for widget elements
            $('[data-bs-toggle="tooltip"]').tooltip();
        }

        refreshWidget($widget) {
            const widgetUrl = $widget.data('url');
            if (!widgetUrl) return;

            // Show loading state
            this.showWidgetLoading($widget);

            $.ajax({
                url: widgetUrl,
                method: 'GET',
                success: (response) => {
                    if (response.data) {
                        $widget.find('.card-body').html(response.data);
                        this.initCounterAnimations();
                        this.showNotification('Widget refreshed successfully', 'success');
                    }
                },
                error: () => {
                    this.showNotification('Failed to refresh widget', 'error');
                },
                complete: () => {
                    this.hideWidgetLoading($widget);
                }
            });
        }

        showWidgetLoading($widget) {
            $widget.find('.card-body').append('<div class="widget-loading"><i class="fa fa-spinner fa-spin"></i></div>');
        }

        hideWidgetLoading($widget) {
            $widget.find('.widget-loading').remove();
        }

        showWidgetSettings($widget) {
            // Placeholder for widget settings modal
            console.log('Widget settings for:', $widget.attr('id'));
        }

        showNotification(message, type = 'info') {
            if (typeof Botble !== 'undefined' && Botble.showNotification) {
                Botble.showNotification(message, type);
            } else if (typeof toastr !== 'undefined') {
                toastr[type](message);
            } else {
                alert(message);
            }
        }

        formatCurrency(amount, currency = 'USD') {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency
            }).format(amount);
        }

        formatNumber(number) {
            return new Intl.NumberFormat().format(number);
        }

        formatPercentage(value, decimals = 1) {
            return (value * 100).toFixed(decimals) + '%';
        }
    }

    // Widget-specific functionality for different widget types
    class FinancialMetricsWidget {
        constructor($widget) {
            this.$widget = $widget;
            this.init();
        }

        init() {
            this.bindEvents();
            this.updateMetrics();
        }

        bindEvents() {
            // Add any specific event handlers for financial metrics
        }

        updateMetrics() {
            // Update financial metrics display
            this.animateMetrics();
        }

        animateMetrics() {
            this.$widget.find('.metric-value').each(function() {
                const $metric = $(this);
                const value = parseFloat($metric.data('value') || $metric.text().replace(/[^0-9.-]/g, ''));
                
                if (!isNaN(value)) {
                    $metric.prop('Counter', 0).animate({
                        Counter: value
                    }, {
                        duration: 2000,
                        easing: 'easeOutQuart',
                        step: function(now) {
                            $metric.text(now.toFixed(2));
                        }
                    });
                }
            });
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize main widgets controller
        const widgetsController = new ExpenseCalculationsWidgets();

        // Initialize specific widget types
        $('.financial-metrics-widget').each(function() {
            new FinancialMetricsWidget($(this));
        });

        // Auto-refresh widgets every 5 minutes
        setInterval(() => {
            $('.widget-item[data-auto-refresh="true"]').each(function() {
                widgetsController.refreshWidget($(this));
            });
        }, 300000); // 5 minutes
    });

})(jQuery);
