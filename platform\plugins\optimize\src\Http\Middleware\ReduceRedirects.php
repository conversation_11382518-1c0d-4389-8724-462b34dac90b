<?php

namespace Shaqi\AdvancedOptimize\Http\Middleware;

use Botble\Optimize\Http\Middleware\PageSpeed;

class ReduceRedirects extends PageSpeed
{
    public function apply(string $buffer): string
    {
        $path = request()->path();
        if ($path !== '/' && substr($path, -1) === '/') {
            return redirect(rtrim(request()->getPathInfo(), '/'), 301)->getContent();
        }

        return $buffer;
    }
}
