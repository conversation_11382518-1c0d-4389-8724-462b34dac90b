<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Http\Controllers;

use Bo<PERSON>ble\Base\Facades\Assets;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use <PERSON><PERSON>ble\Base\Widgets\Contracts\AdminWidget;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Botble\ExpenseCalculations\Services\ExpenseCalculationService;
use Illuminate\Http\Request;

class DashboardController extends BaseController
{
    protected ExpenseCalculationService $expenseCalculationService;

    public function __construct()
    {
        $this->expenseCalculationService = app('expense-calculation-service');
    }

    public function index(Request $request, AdminWidget $widget)
    {
        $this->pageTitle(trans('plugins/expense-calculations::expense-calculations.name'));

        Assets::addScriptsDirectly([
            'vendor/core/plugins/ecommerce/libraries/daterangepicker/daterangepicker.js',
            'vendor/core/plugins/expense-calculations/js/report.js',
        ])
            ->addStylesDirectly([
                'vendor/core/plugins/ecommerce/libraries/daterangepicker/daterangepicker.css',
                'vendor/core/plugins/expense-calculations/css/report.css',
            ])
            ->addScripts(['moment', 'apexchart'])
            ->addStyles(['apexchart']);

        Assets::usingVueJS();

        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        if ($request->ajax()) {
            return $this
                ->httpResponse()->setData(view('plugins/expense-calculations::reports.ajax', compact('widget'))->render());
        }

        return view(
            'plugins/expense-calculations::reports.index',
            compact('startDate', 'endDate', 'widget')
        );
    }

    public function widget(Request $request)
    {
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $data = $this->expenseCalculationService->getDashboardData($startDate, $endDate);

        return $this
            ->httpResponse()
            ->setData(
                view('plugins/expense-calculations::reports.widgets.general', compact('data'))->render()
            );
    }
}
