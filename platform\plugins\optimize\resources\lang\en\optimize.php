<?php

return [
    'name' => 'Optimizes',
    'settings' => [
        'title' => 'Advanced Optimization',
        'description' => 'Preload style,lazy loading, alt attributes...',
        'enable' => 'Enable optimize page speed?',
    ],

    'preload_style' => 'Preload styles',
    'preload_style_description' => 'Preload the execution of styles in the HTML. If necessary cancel preloads in some styles, use data-pagespeed-no-preload as style attribute to cancel preloading.',
    'alt_attribute' => 'Add alt attribute',
    'alt_attribute_description' => 'All images with a blank alt attribute or missing alt attribute will have their alt attribute automatically populated with the image file name.',
    'lazy_loading' => 'Lazy loading',
    'lazy_loading_description' => 'All images without loading="lazy" attribute will be lazy loaded. Use data-pagepeed-no-lazy as attribute to cancel lazy loading.',
    'reduce_dom_size' => 'Reduce DOM Size',
    'reduce_dom_size_description' => 'Ensure that the HTML DOM size is minimized for faster parsing',
    'trim_url' => 'Trim URL',
    'trim_url_description' => 'Remove the https: or http: protocol',
    'reduce_redirects' => 'Reduce Redirects',
    'reduce_redirects_description' => 'Minimize unnecessary redirects to reduce additional round-trip time',

];
