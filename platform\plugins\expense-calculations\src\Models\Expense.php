<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Models;

use Botble\Base\Casts\SafeContent;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Expense extends BaseModel
{
    protected $table = 'ec_expenses';

    protected $fillable = [
        'title',
        'description',
        'amount',
        'category',
        'expense_date',
        'reference_number',
        'attachments',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'title' => SafeContent::class,
        'description' => SafeContent::class,
        'amount' => 'decimal:2',
        'expense_date' => 'date',
        'attachments' => 'json',
    ];

    protected function amountFormat(): Attribute
    {
        return Attribute::get(fn () => format_price($this->amount));
    }

    public function scopeActive($query)
    {
        return $query->where('status', BaseStatusEnum::PUBLISHED);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('expense_date', [$startDate, $endDate]);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public static function getTotalExpenses($startDate = null, $endDate = null)
    {
        $query = static::active();
        
        if ($startDate && $endDate) {
            $query->byDateRange($startDate, $endDate);
        }
        
        return $query->sum('amount');
    }

    public static function getExpensesByCategory($startDate = null, $endDate = null)
    {
        $query = static::active();
        
        if ($startDate && $endDate) {
            $query->byDateRange($startDate, $endDate);
        }
        
        return $query->selectRaw('category, SUM(amount) as total')
            ->groupBy('category')
            ->get();
    }
}
