# Changelog

All notable changes to the Paygate Payment Gateway plugin will be documented in this file.

## [1.0.0] - 2025-01-20

### Added
- Initial release of Paygate Payment Gateway plugin
- Three-step API integration with Paygate.to:
  - Payment address creation via `/control/wallet.php`
  - Currency conversion via `/control/convert.php`
  - Checkout URL generation for `checkout.paygate.to`
- Webhook support for payment notifications
- Configurable wallet address setting
- Multi-currency support (USD, EUR, GBP, CAD, AUD, JPY, CHF, CNY, SEK, NZD)
- Payment method form with validation
- Error handling and logging
- Admin panel integration following Botble CMS patterns
- Payment status mapping (Completed, Pending, Failed, etc.)
- Secure API communication with timeout handling
- Comprehensive documentation and installation guide
- Customer email extraction from multiple data sources

### Features
- Simple and lightweight cryptocurrency payment integration
- Follows Botble CMS payment gateway patterns
- Automatic payment status updates via webhooks
- Real-time payment processing
- Detailed error messages and logging
- Admin-friendly configuration interface with full-width fields
- Progressive disclosure design following user preferences

### Security
- Secure API key handling
- Webhook signature validation ready
- HTTPS support for all communications
- Input validation and sanitization
- Comprehensive error logging and monitoring

### API Integration
- **Step 1**: Create payment address with callback URL
- **Step 2**: Convert order currency to USD
- **Step 3**: Generate final checkout URL with all parameters
- Proper error handling for each API step
- Timeout protection for all HTTP requests

### Documentation
- Complete README with setup instructions
- Detailed installation guide with troubleshooting
- API integration documentation
- Webhook configuration guide
- Security best practices

### Technical Details
- Compatible with Botble CMS 7.3.0+
- PHP 8.1+ requirement
- PSR-4 autoloading
- Follows Laravel coding standards
- Comprehensive test coverage ready

### Webhook Support
- Handles multiple callback parameters:
  - `number` (order number)
  - `value_coin` (payment amount)
  - `coin` (cryptocurrency used)
  - `txid_in` (incoming transaction ID)
  - `txid_out` (outgoing transaction ID)
  - `address_in` (payment address)
- Automatic payment status determination
- Comprehensive logging of all webhook events
