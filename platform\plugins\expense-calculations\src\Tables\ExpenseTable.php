<?php

namespace Bo<PERSON><PERSON>\ExpenseCalculations\Tables;


use Bo<PERSON>ble\ExpenseCalculations\Models\Expense;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Bo<PERSON>ble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\FormattedColumn;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class ExpenseTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Expense::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('expense-calculations.expenses.create'))
            ->addColumns([
                IdColumn::make(),
                NameColumn::make('title')->route('expense-calculations.expenses.edit'),
                FormattedColumn::make('amount')
                    ->title(trans('plugins/expense-calculations::expenses.amount'))
                    ->alignEnd()
                    ->renderUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();
                        return format_price($item->amount);
                    }),
                Column::make('category')
                    ->title(trans('plugins/expense-calculations::expenses.category')),
                FormattedColumn::make('expense_date')
                    ->title(trans('plugins/expense-calculations::expenses.expense_date'))
                    ->renderUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();
                        return $item->expense_date->format('Y-m-d');
                    }),
                StatusColumn::make(),
                CreatedAtColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('expense-calculations.expenses.edit'),
                DeleteAction::make()->route('expense-calculations.expenses.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('expense-calculations.expenses.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'title',
                        'amount',
                        'category',
                        'expense_date',
                        'status',
                        'created_at',
                    ]);
            });
    }
}
