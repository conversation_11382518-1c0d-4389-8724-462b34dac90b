<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Widgets;

use Bo<PERSON>ble\Base\Widgets\Html;
use Bo<PERSON>ble\ExpenseCalculations\Services\ExpenseCalculationService;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Illuminate\Http\Request;

class ExpenseCalculationsWidget extends Html
{
    protected ExpenseCalculationService $expenseCalculationService;

    public function __construct()
    {
        $this->expenseCalculationService = app('expense-calculation-service');
    }

    public function getContent(): string
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $data = $this->expenseCalculationService->getDashboardData($startDate, $endDate);

        return view('plugins/expense-calculations::reports.widgets.general', compact('data'))->render();
    }
}
