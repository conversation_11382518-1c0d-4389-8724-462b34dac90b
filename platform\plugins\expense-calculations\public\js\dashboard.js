// Expense Calculations Report functionality - similar to ecommerce reports
$(() => {
    if (!window.moment || !jQuery().daterangepicker) {
        return
    }

    moment.locale($('html').attr('lang'))

    let $dateRange = $(document).find('.date-range-picker')
    let dateFormat = $dateRange.data('format') || 'YYYY-MM-DD'
    let startDate = $dateRange.data('start-date') || moment().subtract(29, 'days')

    let today = moment()
    let endDate = moment().endOf('month')
    if (endDate > today) {
        endDate = today
    }

    let rangesTrans = BotbleVariables.languages.reports
    let ranges = {
        [rangesTrans.today]: [today, today],
        [rangesTrans.this_week]: [moment().startOf('week'), today],
        [rangesTrans.last_7_days]: [moment().subtract(6, 'days'), today],
        [rangesTrans.last_30_days]: [moment().subtract(29, 'days'), today],
        [rangesTrans.this_month]: [moment().startOf('month'), endDate],
        [rangesTrans.this_year]: [moment().startOf('year'), moment().endOf('year')],
    }

    $dateRange.daterangepicker(
        {
            ranges: ranges,
            alwaysShowCalendars: true,
            startDate: startDate,
            endDate: endDate,
            maxDate: endDate,
            opens: 'left',
            drops: 'auto',
            locale: {
                format: dateFormat,
            },
            autoUpdateInput: false,
        },
        function (start, end, label) {
            $.ajax({
                url: $dateRange.data('href'),
                data: {
                    date_from: start.format('YYYY-MM-DD'),
                    date_to: end.format('YYYY-MM-DD'),
                    predefined_range: label,
                },
                type: 'GET',
                success: (data) => {
                    if (data.error) {
                        Botble.showError(data.message)
                    } else {
                        if (!$('#report-stats-content').length) {
                            const newUrl = new URL(window.location.href)

                            newUrl.searchParams.set('date_from', start.format('YYYY-MM-DD'))
                            newUrl.searchParams.set('date_to', end.format('YYYY-MM-DD'))

                            history.pushState({ urlPath: newUrl.href }, '', newUrl.href)

                            window.location.reload()
                        } else {
                            $('.widget-item').each((key, widget) => {
                                const widgetEl = $(widget).prop('id')
                                $(`#${widgetEl}`).replaceWith($(data.data).find(`#${widgetEl}`))
                            })
                        }

                        $dateRange.html(
                            $dateRange.data('format-value')
                                .replace('__from__', start.format(dateFormat))
                                .replace('__to__', end.format(dateFormat))
                        )
                    }
                },
                error: () => {
                    Botble.showError('Something went wrong!')
                }
            })
        }
    )

    $dateRange.on('apply.daterangepicker', function (ev, picker) {
        $(this).val(picker.startDate.format(dateFormat) + ' - ' + picker.endDate.format(dateFormat))
    })

    $dateRange.on('cancel.daterangepicker', function (ev, picker) {
        $(this).val('')
    })
})

// Widget-specific functionality
class ExpenseCalculationsWidget {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.animateCounters();
    }

    bindEvents() {
        // Refresh widget data
        $(document).on('click', '.widget-refresh', (e) => {
            e.preventDefault();
            this.refreshWidget();
        });
    }

    animateCounters() {
        // Animate number counters
        $('.metric-value').each(function() {
            const $this = $(this);
            const countTo = $this.text().replace(/[^0-9.-]+/g, '');

            if (countTo && !isNaN(countTo)) {
                $({ countNum: 0 }).animate({
                    countNum: countTo
                }, {
                    duration: 2000,
                    easing: 'linear',
                    step: function() {
                        $this.text(Math.floor(this.countNum));
                    },
                    complete: function() {
                        $this.text(countTo);
                    }
                });
            }
        });
    }

    refreshWidget() {
        // Refresh widget data via AJAX
        const widgetUrl = $('.widget-item').data('url');
        if (widgetUrl) {
            $.get(widgetUrl)
                .done((response) => {
                    if (response.data) {
                        $('.widget-item .card-body').html(response.data);
                        this.animateCounters();
                    }
                })
                .fail(() => {
                    this.showNotification('Failed to refresh widget data', 'error');
                });
        }
    }

    showNotification(message, type = 'success') {
        if (typeof Botble !== 'undefined' && Botble.showNotification) {
            Botble.showNotification(message, type);
        }
    }
}

// Initialize when document is ready
$(document).ready(function() {
    // Initialize dashboard if on dashboard page
    if ($('.expense-calculations-dashboard').length) {
        new ExpenseCalculationsDashboard();
    }

    // Initialize widget if widget is present
    if ($('.expense-calculations-widget').length) {
        new ExpenseCalculationsWidget();
    }
});
