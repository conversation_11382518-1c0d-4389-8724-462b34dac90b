<?php

namespace Botble\ExpenseCalculations\Repositories\Interfaces;

use Botble\Support\Repositories\Interfaces\RepositoryInterface;

interface IncomeInterface extends RepositoryInterface
{
    public function getTotalIncome($startDate = null, $endDate = null);
    
    public function getIncomeBySource($startDate = null, $endDate = null);
    
    public function getIncomeByDateRange($startDate, $endDate);
}
