<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Widgets;

use Bo<PERSON>ble\Base\Widgets\Card;
use <PERSON><PERSON>ble\ExpenseCalculations\Services\ExpenseCalculationService;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Illuminate\Http\Request;

class TotalRevenueCard extends Card
{
    protected ExpenseCalculationService $expenseCalculationService;

    public function __construct()
    {
        $this->expenseCalculationService = app('expense-calculation-service');
    }

    public function getOptions(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $totalRevenue = $this->expenseCalculationService->getTotalRevenue($startDate, $endDate);

        return [
            'series' => [
                [
                    'data' => [$totalRevenue],
                ],
            ],
        ];
    }

    public function getViewData(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $totalRevenue = $this->expenseCalculationService->getTotalRevenue($startDate, $endDate);
        $revenueData = $this->expenseCalculationService->getDailyRevenueData($startDate, $endDate);

        // Ensure we have valid data for the chart
        $chartData = [];
        $chartDates = [];

        if (!empty($revenueData)) {
            // Sort by date to ensure proper order
            ksort($revenueData);

            foreach ($revenueData as $date => $value) {
                $chartDates[] = $date;
                $chartData[] = is_numeric($value) ? (float) $value : 0;
            }
        } else {
            // If no data, create a single point with zero value for the date range
            $dateStr = $startDate ? ($startDate instanceof \DateTime ? $startDate->format('Y-m-d') : $startDate) : date('Y-m-d');
            $chartDates[] = $dateStr;
            $chartData[] = 0;
        }

        return array_merge(parent::getViewData(), [
            'content' => view(
                'plugins/expense-calculations::reports.widgets.total-revenue-card',
                [
                    'totalRevenue' => $totalRevenue,
                    'result' => 0, // You can calculate percentage change here
                    'chartData' => $chartData,
                    'chartDates' => $chartDates,
                ]
            )->render(),
        ]);
    }
}
