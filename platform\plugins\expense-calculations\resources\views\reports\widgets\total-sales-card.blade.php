<x-core::card class="analytic-card">
    <x-core::card.body class="p-3">
        <div class="row align-items-center">
            <div class="col-auto">
                <x-core::icon
                    class="text-white bg-blue rounded p-1"
                    name="ti ti-shopping-cart"
                    size="md"
                />
            </div>
            <div class="col mt-0">
                <p class="text-secondary mb-0 fs-4">
                    {{ trans('plugins/expense-calculations::expense-calculations.total_sales_count') }}
                </p>
                <h3 class="mb-n1 fs-1">
                    {{ format_price($totalSales) }}
                </h3>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <div class="rp-card-chart position-relative" style="height: 100px;">
                    <div id="total-sales-chart"></div>
                </div>
            </div>
        </div>
    </x-core::card.body>
    @include('plugins/expense-calculations::reports.widgets.card-description')
</x-core::card>

<script>
    $(document).ready(function() {
        if (document.querySelector('#total-sales-chart')) {
            var chartData = @json($chartData);
            var chartDates = @json($chartDates);

            // Validate and clean data
            var validData = [];
            var validDates = [];

            for (var i = 0; i < chartData.length; i++) {
                var value = parseFloat(chartData[i]);
                if (!isNaN(value) && isFinite(value)) {
                    validData.push(value);
                    validDates.push(chartDates[i] || new Date().toISOString().split('T')[0]);
                }
            }

            // Ensure we have at least one data point
            if (validData.length === 0) {
                validData = [0];
                validDates = [new Date().toISOString().split('T')[0]];
            }

            var chartConfig = {
                series: [{
                    name: '{{ trans('plugins/expense-calculations::expense-calculations.total_sales_count') }}',
                    data: validData
                }],
                chart: {
                    height: 100,
                    type: validData.length === 1 ? 'bar' : 'area',
                    toolbar: { show: false },
                    sparkline: { enabled: true }
                },
                dataLabels: { enabled: false },
                stroke: {
                    curve: 'smooth',
                    width: 2
                },
                colors: ['#007bff'],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.1,
                    }
                },
                xaxis: {
                    type: validData.length === 1 ? 'category' : 'datetime',
                    categories: validDates,
                    labels: { show: false },
                    axisBorder: { show: false },
                    axisTicks: { show: false }
                },
                yaxis: { show: false },
                grid: { show: false },
                tooltip: {
                    x: { format: 'dd/MM/yy' },
                    y: {
                        formatter: function(value) {
                            return new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: 'USD'
                            }).format(value);
                        }
                    }
                }
            };

            new ApexCharts(document.querySelector('#total-sales-chart'), chartConfig).render();
        }
    });
</script>
