<?php

namespace Shaqi\AdvancedOptimize\Http\Middleware;

use Botble\Optimize\Http\Middleware\PageSpeed;
use DOMDocument;
use DOMXPath;

class TrimUrls extends PageSpeed
{
    public function apply(string $buffer): string
    {
        libxml_use_internal_errors(true);
        $dom = new DOMDocument();
        $dom->loadHTML($buffer, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $xpath = new DOMXPath($dom);

        // Tags and attributes to check
        $targets = [
            'a' => 'href',
            'link' => 'href',
            'script' => 'src',
            'img' => 'src',
            'iframe' => 'src',
            'video' => 'src',
            'source' => 'src',
            'audio' => 'src',
        ];

        foreach ($targets as $tag => $attr) {
            foreach ($xpath->query("//{$tag}[@{$attr}]") as $node) {
                $url = $node->getAttribute($attr);

                // Only trim if it starts with http(s) and is same-domain or absolute
                if (preg_match('/^https?:\\/\\//', $url)) {
                    // Skip canonical, og:url, etc.
                    if (!preg_match('/rel=\"canonical\"|og:url|twitter:url/i', $dom->saveHTML($node))) {
                        // Convert to protocol-relative
                        $url = preg_replace('/^https?:/', '', $url);
                        $node->setAttribute($attr, $url);
                    }
                }
            }
        }

        return $dom->saveHTML();
    }
}
