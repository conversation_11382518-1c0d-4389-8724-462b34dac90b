<?php

namespace Botble\ExpenseCalculations\Models;

use Botble\Base\Casts\SafeContent;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Income extends BaseModel
{
    protected $table = 'ec_income';

    protected $fillable = [
        'title',
        'description',
        'amount',
        'source',
        'income_date',
        'reference_number',
        'attachments',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'title' => SafeContent::class,
        'description' => SafeContent::class,
        'amount' => 'decimal:2',
        'income_date' => 'date',
        'attachments' => 'json',
    ];

    protected function amountFormat(): Attribute
    {
        return Attribute::get(fn () => format_price($this->amount));
    }

    public function scopeActive($query)
    {
        return $query->where('status', BaseStatusEnum::PUBLISHED);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('income_date', [$startDate, $endDate]);
    }

    public function scopeBySource($query, $source)
    {
        return $query->where('source', $source);
    }

    public static function getTotalIncome($startDate = null, $endDate = null)
    {
        $query = static::active();
        
        if ($startDate && $endDate) {
            $query->byDateRange($startDate, $endDate);
        }
        
        return $query->sum('amount');
    }

    public static function getIncomeBySource($startDate = null, $endDate = null)
    {
        $query = static::active();
        
        if ($startDate && $endDate) {
            $query->byDateRange($startDate, $endDate);
        }
        
        return $query->selectRaw('source, SUM(amount) as total')
            ->groupBy('source')
            ->get();
    }
}
