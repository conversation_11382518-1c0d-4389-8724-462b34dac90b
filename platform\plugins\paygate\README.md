# Paygate Payment Gateway Plugin

A comprehensive cryptocurrency payment gateway plugin for Botble CMS that integrates with Paygate.to to process cryptocurrency payments.

## Features

- **Cryptocurrency Payments**: Accept various cryptocurrencies including Bitcoin, Ethereum, and more
- **Multi-Currency Support**: Automatic currency conversion to USD
- **Secure Payment Processing**: Three-step API integration for secure transactions
- **Webhook Support**: Real-time payment notifications and status updates
- **Admin Integration**: Easy configuration through Botble CMS admin panel
- **Payment Tracking**: Comprehensive logging and transaction tracking

## Requirements

- Botble CMS 7.3.0 or higher
- PHP 8.1 or higher
- Payment plugin enabled
- Active Paygate.to account

## Installation

1. **Download and Extract**
   ```bash
   # Extract the plugin to your Botble plugins directory
   cp -r paygate platform/plugins/
   ```

2. **Install Dependencies**
   ```bash
   composer install
   ```

3. **Activate Plugin**
   - Go to Admin Panel → Plugins
   - Find "Paygate Payment Gateway" and click Activate

4. **Configure Settings**
   - Navigate to Admin Panel → Payments → Payment Methods
   - Find Paygate section and configure:
     - **Wallet Address**: Your Paygate wallet address
     - **Status**: Enable the payment method
     - **Available Countries**: (Optional) Restrict to specific countries

## Configuration

### Required Settings

- **Wallet Address**: Your Paygate.to wallet address (required)

### Optional Settings

- **Payment Method Name**: Customize the display name (default: "Paygate")
- **Description**: Add custom description for customers
- **Payment Fee**: Set additional fees if needed
- **Available Countries**: Restrict to specific countries if needed

## API Integration

The plugin implements a three-step API integration process:

1. **Create Payment Address**: Generates a unique payment address for the transaction
2. **Currency Conversion**: Converts order amount to USD using current exchange rates
3. **Generate Checkout URL**: Creates the final payment URL for customer redirection

## Webhook Configuration

The plugin automatically handles webhook notifications from Paygate. The webhook URL is:
```
https://yourdomain.com/payment/paygate/webhook
```

Configure this URL in your Paygate dashboard to receive payment notifications.

## Supported Currencies

- USD (US Dollar)
- EUR (Euro)
- GBP (British Pound)
- CAD (Canadian Dollar)
- AUD (Australian Dollar)
- JPY (Japanese Yen)
- CHF (Swiss Franc)
- CNY (Chinese Yuan)
- SEK (Swedish Krona)
- NZD (New Zealand Dollar)

## Security

- Secure API communication with timeout handling
- Webhook signature validation ready
- HTTPS support for all communications
- Input validation and sanitization
- Comprehensive error logging

## Support

For support and documentation, visit:
- [Paygate.to](https://paygate.to)
- [Botble CMS Documentation](https://docs.botble.com)

## License

This plugin is licensed under the MIT License.
