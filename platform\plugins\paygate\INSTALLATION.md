# Paygate Payment Gateway - Installation Guide

This guide will walk you through the complete installation and configuration process for the Paygate Payment Gateway plugin.

## Prerequisites

Before installing the Paygate plugin, ensure you have:

- Botble CMS version 7.3.0 or higher
- PHP 8.1 or higher
- Payment plugin activated
- Active Paygate.to account with wallet address

## Step-by-Step Installation

### 1. Plugin Installation

1. **Download the Plugin**
   - Extract the paygate plugin folder to `platform/plugins/paygate`

2. **Install Dependencies**
   ```bash
   cd /path/to/your/botble/project
   composer install
   ```

3. **Activate the Plugin**
   - Log into your Botble admin panel
   - Navigate to **Plugins** → **Installed Plugins**
   - Find "Paygate Payment Gateway" in the list
   - Click the **Activate** button

### 2. Payment Method Configuration

1. **Access Payment Settings**
   - Go to **Admin Panel** → **Payments** → **Payment Methods**
   - Scroll down to find the **Paygate** section

2. **Required Settings**
   - **Status**: Set to "Enable" to activate the payment method
   - **Wallet Address**: Enter your Paygate.to wallet address

3. **Optional Settings**
   - **Payment Method Name**: Customize the display name (default: "Paygate")
   - **Description**: Add custom description for customers
   - **Payment Fee**: Set additional fees if needed
   - **Available Countries**: Restrict to specific countries if needed

### 3. Paygate Dashboard Setup

1. **Log into Paygate Dashboard**
   - Visit [https://paygate.to](https://paygate.to)
   - Log into your account

2. **Configure Webhook**
   - Navigate to **Settings** or **Webhooks** section
   - Add the webhook URL: `https://yourdomain.com/payment/paygate/webhook`
   - Replace `yourdomain.com` with your actual domain

3. **Copy Wallet Address**
   - Locate your wallet address in the dashboard
   - Copy it and paste into the plugin settings

### 4. Testing

1. **Create Test Order**
   - Create a test product/order on your website
   - Proceed to checkout

2. **Select Paygate Payment**
   - Choose Paygate as the payment method
   - Complete the checkout process

3. **Verify Payment Flow**
   - Ensure you're redirected to Paygate checkout
   - Complete a small test transaction
   - Verify the payment status updates correctly in your admin panel

## Troubleshooting

### Common Issues

1. **Plugin Not Appearing**
   - Ensure the Payment plugin is activated first
   - Check file permissions on the plugin directory
   - Clear cache: `php artisan cache:clear`

2. **Wallet Address Error**
   - Verify the wallet address is correct
   - Ensure there are no extra spaces or characters
   - Check that the wallet address is active in Paygate

3. **Webhook Not Working**
   - Verify the webhook URL is correctly configured in Paygate
   - Check server logs for any errors
   - Ensure your server can receive POST requests

4. **Payment Redirection Issues**
   - Check if the Paygate API is accessible from your server
   - Verify SSL certificates are properly configured
   - Check firewall settings

### Debug Mode

To enable debug logging:

1. Check payment logs in **Admin Panel** → **Payments** → **Logs**
2. Look for Paygate-related entries
3. Check Laravel logs in `storage/logs/laravel.log`

### Support

If you encounter issues:

1. Check the plugin logs for error messages
2. Verify all configuration settings
3. Test with a different browser or device
4. Contact Paygate support for API-related issues

## Security Considerations

- Always use HTTPS for production environments
- Keep your Paygate credentials secure
- Regularly monitor payment logs for suspicious activity
- Update the plugin when new versions are available

## Next Steps

After successful installation:

1. Configure additional payment methods if needed
2. Set up proper backup procedures
3. Monitor payment transactions regularly
4. Consider setting up automated reporting

For additional support, refer to the main README.md file or contact support.
