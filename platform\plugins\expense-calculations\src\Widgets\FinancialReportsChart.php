<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Widgets;

use Botble\Base\Widgets\Html;
use Bo<PERSON>ble\ExpenseCalculations\Services\ExpenseCalculationService;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Illuminate\Http\Request;

class FinancialReportsChart extends Html
{
    protected ExpenseCalculationService $expenseCalculationService;

    public function __construct()
    {
        $this->expenseCalculationService = app('expense-calculation-service');
    }

    public function getContent(): string
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $salesReport = $this->expenseCalculationService->getFinancialChartData($startDate, $endDate);
        $count = [
            'revenues' => $this->expenseCalculationService->getRevenueBreakdownData($startDate, $endDate),
        ];

        // Helper function for revenue data
        $revenues = function ($key) use ($count) {
            return collect($count['revenues'])->pluck($key)->toArray();
        };

        return view('plugins/expense-calculations::reports.widgets.financial-chart', compact(
            'salesReport',
            'count',
            'revenues'
        ))->render();
    }
}
