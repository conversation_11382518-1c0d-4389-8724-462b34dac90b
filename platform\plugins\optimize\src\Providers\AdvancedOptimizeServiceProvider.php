<?php

namespace Shaqi\AdvancedOptimize\Providers;

use Bo<PERSON>ble\Base\Facades\PanelSectionManager;
use Bo<PERSON>ble\Base\PanelSections\PanelSectionItem;
use <PERSON><PERSON><PERSON>\Setting\PanelSections\SettingCommonPanelSection;
use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use Botble\Base\Traits\LoadAndPublishDataTrait;
use Bo<PERSON>ble\Optimize\Facades\OptimizerHelper;
use Illuminate\Routing\Events\RouteMatched;
use Shaqi\AdvancedOptimize\Http\Middleware\AltAttribute;
use Shaqi\AdvancedOptimize\Http\Middleware\HTTP2Push;
use Shaqi\AdvancedOptimize\Http\Middleware\LazyLoad;
use Shaqi\AdvancedOptimize\Http\Middleware\PreloadStyles;
use Shaqi\AdvancedOptimize\Http\Middleware\ReduceDomSize;
use Shaqi\AdvancedOptimize\Http\Middleware\ReduceRedirects;
use <PERSON><PERSON>qi\AdvancedOptimize\Http\Middleware\TrimUrls;

class AdvancedOptimizeServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/optimize')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions'])
            ->loadAndPublishTranslations()
            ->loadRoutes()
            ->loadAndPublishViews();

            PanelSectionManager::default()->beforeRendering(function () {
                PanelSectionManager::registerItem(
                    SettingCommonPanelSection::class,
                    fn () => PanelSectionItem::make('other')
                        ->setTitle(trans('plugins/optimize::optimize.settings.title'))
                        ->withIcon('ti ti-brand-speedtest')
                        ->withPriority(141)
                        ->withDescription(trans('plugins/optimize::optimize.settings.description'))
                        ->withRoute('advanced.optimize.settings')
                );
            });

            $this->app['events']->listen(RouteMatched::class, function () {
                if (OptimizerHelper::isEnabled()) {
                    /**
                     * @var Router $router
                     */
                    $router = $this->app['router'];



                    if (setting('optimize_preload_style', 0)) {
                        $router->pushMiddlewareToGroup('web', PreloadStyles::class);
                    }
                    if (setting('optimize_alt_attribute', 0)) {
                        $router->pushMiddlewareToGroup('web', AltAttribute::class);
                    }
                    if (setting('optimize_lazy_loading', 0)) {
                        $router->pushMiddlewareToGroup('web', LazyLoad::class);
                    }
                    if (setting('optimize_reduce_dom_size', 0)) {
                        $router->pushMiddlewareToGroup('web', ReduceDomSize::class);
                    }
                    if (setting('optimize_trim_url', 0)) {
                        $router->pushMiddlewareToGroup('web', TrimUrls::class);
                    }

                    // $router->pushMiddlewareToGroup('web', HTTP2Push::class);
                    if (setting('optimize_reduce_redirects', 0)) {
                    $router->pushMiddlewareToGroup('web', ReduceRedirects::class);
                    }

                }
            });
    }
}
