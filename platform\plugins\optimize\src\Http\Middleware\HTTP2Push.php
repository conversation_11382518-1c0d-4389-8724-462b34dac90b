<?php

namespace Shaqi\AdvancedOptimize\Http\Middleware;

use Botble\Optimize\Http\Middleware\PageSpeed;

class HTTP2Push extends PageSpeed
{
    public function apply(string $buffer): string
    {
        $response = response($buffer); // Create a response instance with the content
        $response->headers->set('Link', '</css/app.css>; rel=preload; as=style, </js/app.js>; rel=preload; as=script');

        return $response->getContent(); // Return the modified content
    }
}
