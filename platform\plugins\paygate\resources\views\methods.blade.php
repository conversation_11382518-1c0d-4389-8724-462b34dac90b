@if (setting('payment_paygate_status') == 1)
    <x-plugins-payment::payment-method
        :name="PAYGATE_PAYMENT_METHOD_NAME"
        paymentName="Paygate"
        :supportedCurrencies="(new Botble\Paygate\Services\Gateways\PaygatePaymentService)->supportedCurrencyCodes()"
    >
        <div class="paygate-payment-info">
            <p class="text-muted">{{ __('You will be redirected to Paygate to complete your cryptocurrency payment securely.') }}</p>
            <div class="payment-crypto-info mt-2">
                <small class="text-muted">
                    {{ __('Accepts various cryptocurrencies including Bitcoin, Ethereum, and more.') }}
                </small>
            </div>
        </div>
    </x-plugins-payment::payment-method>
@endif
