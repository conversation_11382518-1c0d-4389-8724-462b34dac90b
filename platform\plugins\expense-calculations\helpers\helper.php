<?php

if (! function_exists('expense_calculations_path')) {
    function expense_calculations_path(string $path = ''): string
    {
        return plugin_path('expense-calculations' . ($path ? DIRECTORY_SEPARATOR . $path : ''));
    }
}

if (! function_exists('expense_calculations_asset')) {
    function expense_calculations_asset(string $path): string
    {
        return asset('vendor/core/plugins/expense-calculations/' . $path);
    }
}

if (! function_exists('get_expense_categories')) {
    function get_expense_categories(): array
    {
        return [
            'office_supplies' => 'Office Supplies',
            'marketing' => 'Marketing',
            'travel' => 'Travel',
            'utilities' => 'Utilities',
            'rent' => 'Rent',
            'insurance' => 'Insurance',
            'professional_services' => 'Professional Services',
            'equipment' => 'Equipment',
            'software' => 'Software',
            'other' => 'Other',
        ];
    }
}

if (! function_exists('get_income_sources')) {
    function get_income_sources(): array
    {
        return [
            'consulting' => 'Consulting',
            'affiliate' => 'Affiliate',
            'investment' => 'Investment',
            'rental' => 'Rental',
            'commission' => 'Commission',
            'royalty' => 'Royalty',
            'grant' => 'Grant',
            'other' => 'Other',
        ];
    }
}
