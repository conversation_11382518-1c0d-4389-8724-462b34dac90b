<?php

use <PERSON><PERSON>ble\Base\Facades\AdminHelper;
use Bo<PERSON>ble\ExpenseCalculations\Http\Controllers\DashboardController;
use Botble\ExpenseCalculations\Http\Controllers\ExpenseController;
use Botble\ExpenseCalculations\Http\Controllers\IncomeController;
use Illuminate\Support\Facades\Route;

AdminHelper::registerRoutes(function (): void {
    Route::group(['namespace' => 'Botble\ExpenseCalculations\Http\Controllers', 'prefix' => 'expense-calculations'], function (): void {
        Route::get('reports', [DashboardController::class, 'index'])->name('expense-calculations.dashboard');
        Route::get('dashboard-widget-general', [DashboardController::class, 'widget'])->name('expense-calculations.dashboard-widget');

        Route::group(['prefix' => 'expenses', 'as' => 'expense-calculations.expenses.'], function (): void {
            Route::resource('', ExpenseController::class)->parameters(['' => 'expense']);
        });

        Route::group(['prefix' => 'income', 'as' => 'expense-calculations.income.'], function (): void {
            Route::resource('', IncomeController::class)->parameters(['' => 'income']);
        });
    });
});
