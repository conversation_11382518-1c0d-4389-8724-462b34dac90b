<x-core::form.on-off.checkbox
    name="optimize_page_speed_enable"
    :label="trans('packages/optimize::optimize.settings.enable')"
    :checked="setting('optimize_page_speed_enable', false)"
    data-bb-toggle="collapse"
    data-bb-target=".optimize-settings"
    :wrapper="false"
/>

<x-core::form.fieldset
    data-bb-value="1"
    class="optimize-settings mt-3"
    @style(['display: none;' => ! setting('optimize_page_speed_enable', false)])
>

    <x-core::form.on-off.checkbox
    name="optimize_preload_style"
    :label="trans('plugins/optimize::optimize.preload_style')"
    :checked="setting('optimize_preload_style', false)"
    :helper-text="trans('plugins/optimize::optimize.preload_style_description')"
    />
    <x-core::form.on-off.checkbox
        name="optimize_alt_attribute"
        :label="trans('plugins/optimize::optimize.alt_attribute')"
        :checked="setting('optimize_alt_attribute', false)"
        :helper-text="trans('plugins/optimize::optimize.alt_attribute_description')"
    />
    <x-core::form.on-off.checkbox
        name="optimize_lazy_loading"
        :label="trans('plugins/optimize::optimize.lazy_loading')"
        :checked="setting('optimize_lazy_loading', false)"
        :helper-text="trans('plugins/optimize::optimize.lazy_loading_description')"
    />
    <x-core::form.on-off.checkbox
        name="optimize_reduce_dom_size"
        :label="trans('plugins/optimize::optimize.reduce_dom_size')"
        :checked="setting('optimize_reduce_dom_size', false)"
        :helper-text="trans('plugins/optimize::optimize.reduce_dom_size_description')"
    />
    <x-core::form.on-off.checkbox
        name="optimize_trim_url"
        :label="trans('plugins/optimize::optimize.trim_url')"
        :checked="setting('optimize_trim_url', false)"
        :helper-text="trans('plugins/optimize::optimize.trim_url_description')"
    />
    <x-core::form.on-off.checkbox
        name="optimize_reduce_redirects"
        :label="trans('plugins/optimize::optimize.reduce_redirects')"
        :checked="setting('optimize_reduce_redirects', false)"
        :helper-text="trans('plugins/optimize::optimize.reduce_redirects_description')"
    />

</x-core::form.fieldset>
