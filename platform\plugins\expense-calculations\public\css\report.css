/* Report page styles - similar to ecommerce reports */
.report-chart-content {
    margin-bottom: 20px;
}

.report-chart-content .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.report-chart-content .card-title {
    margin-bottom: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Chart container styles */
#expense-calculations-chart,
#revenue-chart,
#profit-chart {
    min-height: 300px;
}

/* Revenue card chart styles */
.rp-card-chart {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
}

.rp-card-information {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
    color: #495057;
}

.rp-card-information .ti {
    font-size: 2rem;
    margin-bottom: 8px;
    opacity: 0.7;
}

.rp-card-information strong {
    font-size: 1.5rem;
    display: block;
    margin-bottom: 5px;
    font-weight: 700;
}

.rp-card-information small {
    font-size: 0.875rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Status indicators */
.rp-card-status {
    margin-top: 15px;
}

.rp-card-status p {
    margin-bottom: 8px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
}

.rp-card-status .ti-circle-filled {
    margin-right: 8px;
    font-size: 0.75rem;
}

/* Date range picker styles */
.date-range-picker {
    border: 1px solid #dee2e6;
    background: #fff;
    color: #495057;
    font-size: 0.875rem;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.date-range-picker:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
    text-decoration: none;
}

.date-range-picker:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    border-color: #80bdff;
}

/* Loading and error states */
.widget-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #6c757d;
}

.widget-loading .fa-spinner {
    margin-right: 8px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.widget-error {
    padding: 20px;
    text-align: center;
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    margin: 20px 0;
}

/* No data state */
.no-data-container {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-data-container .ti {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Responsive design */
@media (max-width: 768px) {
    .rp-card-information strong {
        font-size: 1.25rem;
    }

    .rp-card-status {
        font-size: 0.8rem;
    }

    .date-range-picker {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    #expense-calculations-chart,
    #revenue-chart,
    #profit-chart {
        min-height: 250px;
    }
}

@media (max-width: 576px) {
    .report-chart-content .card-body {
        padding: 15px;
    }

    .rp-card-chart {
        height: 150px;
    }

    .rp-card-information strong {
        font-size: 1.1rem;
    }

    .rp-card-information .ti {
        font-size: 1.5rem;
    }
}
