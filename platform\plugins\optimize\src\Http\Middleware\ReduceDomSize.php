<?php

namespace Shaqi\AdvancedOptimize\Http\Middleware;

use Botble\Optimize\Http\Middleware\PageSpeed;
use DOMDocument;
use DOMXPath;

class ReduceDomSize extends PageSpeed
{
    public function apply(string $buffer): string
    {
        libxml_use_internal_errors(true);
        $dom = new DOMDocument();
        $dom->loadHTML($buffer, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $xpath = new DOMXPath($dom);

        foreach ($xpath->query('//div[not(node()) and not(@*)]') as $div) {
            $div->parentNode->removeChild($div);
        }

        return $dom->saveHTML();
    }
}
