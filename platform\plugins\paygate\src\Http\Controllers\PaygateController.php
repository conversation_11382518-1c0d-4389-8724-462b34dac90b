<?php

namespace Botble\Paygate\Http\Controllers;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Models\Payment;
use Bo<PERSON>ble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Http\Request;

class PaygateController extends BaseController
{
    public function webhook(Request $request)
    {

        try {
            // Log the webhook request for debugging
            PaymentHelper::log(
                PAYGATE_PAYMENT_METHOD_NAME,
                ['webhook_request' => $request->all()],
                ['headers' => $request->headers->all()]
            );

            // Handle potential parameters from Paygate webhook
            $orderNumber = $request->input('order_id');
            $valueCoin = $request->input('value_coin');
            $coin = $request->input('coin');
            $txidIn = $request->input('txid_in');
            $txidOut = $request->input('txid_out');
            $addressIn = $request->input('address_in');

            // Try to find payment by order number or transaction ID
            $payment = null;

            if ($orderNumber) {
                $payment = Payment::query()
                    ->where('order_id', (int)$orderNumber)
                    ->first();
            }

            if (!$payment && $txidIn) {
                // Try to find by transaction ID
                $payment = Payment::query()
                    ->where('charge_id', $txidIn)
                    ->first();
            }

            if (!$payment) {
                PaymentHelper::log(
                    PAYGATE_PAYMENT_METHOD_NAME,
                    ['error' => 'Payment not found'],
                    ['request' => $request->all()]
                );
                return response()->json([
                    'success' => true,
                    'message' => 'Payment not found'
                ]);

            }

            // Determine payment status based on the presence of transaction IDs
            $paymentStatus = PaymentStatusEnum::FAILED;

            if ($txidIn && $txidOut) {
                // Both incoming and outgoing transactions exist - payment completed
                $paymentStatus = PaymentStatusEnum::COMPLETED;
            } elseif ($txidIn) {
                // Only incoming transaction - payment received but not processed
                $paymentStatus = PaymentStatusEnum::PENDING;
            }

            // Update payment status
            $payment->update([
                'status' => $paymentStatus,
                'amount' => $valueCoin ?: $payment->amount,
                'currency' => $payment->currency,
                'charge_id' => $payment->charge_id,
                'order_id' => $payment->order_id,
                'payment_channel' => PAYGATE_PAYMENT_METHOD_NAME,
                'status' => $paymentStatus,
                'customer_id' => $payment->customer_id,
                'customer_type' => $payment->customer_type,
                'description' => "Payment received: $valueCoin $coin, Transaction In: $txidIn, Transaction Out: $txidOut, Address: $addressIn",
            ]);



            PaymentHelper::log(
                PAYGATE_PAYMENT_METHOD_NAME,
                [
                    'message' => 'Payment status updated',
                    'order_id' => $orderNumber,
                    'status' => $paymentStatus->value ?? $paymentStatus,
                    'value_coin' => $valueCoin,
                    'coin' => $coin,
                    'txid_in' => $txidIn,
                    'txid_out' => $txidOut,
                    'address_in' => $addressIn,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Webhook processed successfully'
            ]);
        } catch (Exception $exception) {
            BaseHelper::logError($exception);

            PaymentHelper::log(
                PAYGATE_PAYMENT_METHOD_NAME,
                ['error' => $exception->getMessage()],
                ['trace' => $exception->getTraceAsString()]
            );
            return response()->json([
                'success' => false,
                'message' => 'Error processing webhook: ' . $exception->getMessage()
            ]);
        }
    }
}
