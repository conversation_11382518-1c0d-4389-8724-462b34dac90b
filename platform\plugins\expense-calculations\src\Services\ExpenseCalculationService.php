<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Services;

use Carbon\Carbon;
use Botble\Ecommerce\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\OrderProduct;
use Botble\Ecommerce\Enums\OrderStatusEnum;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\ExpenseCalculations\Models\Income;
use Botble\ExpenseCalculations\Models\Expense;

class ExpenseCalculationService
{
    public function getTotalSalesCount($startDate = null, $endDate = null): float
    {
        try {
            if (is_plugin_active('payment')) {
                // Use payment-based calculation like ecommerce
                $query = Order::query()
                    ->select([
                        DB::raw('SUM(COALESCE(payments.amount, 0) - COALESCE(payments.refunded_amount, 0)) as revenue'),
                    ])
                    ->join('payments', 'payments.id', '=', 'ec_orders.payment_id')
                    ->where('payments.status', PaymentStatusEnum::COMPLETED)
                    ->where('ec_orders.is_finished', true);

                if ($startDate && $endDate) {
                    $startDateStr = is_string($startDate) ? $startDate : $startDate->format('Y-m-d');
                    $endDateStr = is_string($endDate) ? $endDate : $endDate->format('Y-m-d');
                    $query->whereDate('payments.created_at', '>=', $startDateStr)
                          ->whereDate('payments.created_at', '<=', $endDateStr);
                }

                return $query->value('revenue') ?? 0;
            } else {
                // Fallback to order-based calculation
                $query = Order::query()
                    ->where('status', OrderStatusEnum::COMPLETED)
                    ->where('is_finished', true);

                if ($startDate && $endDate) {
                    $query->whereBetween('created_at', [$startDate, $endDate]);
                }

                return $query->sum('amount') ?? 0;
            }
        } catch (\Exception $e) {
            Log::error('Error calculating total sales count: ' . $e->getMessage());
            return 0;
        }
    }

    public function getTotalSaleOfTopSellingItems($startDate = null, $endDate = null): float
    {
        $query = OrderProduct::query()
            ->join('ec_orders', 'ec_orders.id', '=', 'ec_order_product.order_id')
            ->where('ec_orders.status', OrderStatusEnum::COMPLETED)
            ->where('ec_orders.is_finished', true);

        if ($startDate && $endDate) {
            $query->whereBetween('ec_orders.created_at', [$startDate, $endDate]);
        }

        // Get top 10 selling products by quantity
        $topProducts = $query->select('product_id', DB::raw('SUM(qty) as total_qty'))
            ->groupBy('product_id')
            ->orderBy('total_qty', 'desc')
            ->limit(10)
            ->pluck('product_id');

        if ($topProducts->isEmpty()) {
            return 0;
        }

        // Calculate total sales for these top products
        $topProductsSales = OrderProduct::query()
            ->join('ec_orders', 'ec_orders.id', '=', 'ec_order_product.order_id')
            ->where('ec_orders.status', OrderStatusEnum::COMPLETED)
            ->where('ec_orders.is_finished', true)
            ->whereIn('ec_order_product.product_id', $topProducts);

        if ($startDate && $endDate) {
            $topProductsSales->whereBetween('ec_orders.created_at', [$startDate, $endDate]);
        }

        return $topProductsSales->sum(DB::raw('ec_order_product.price * ec_order_product.qty')) ?? 0;
    }

    public function getTotalExpenses($startDate = null, $endDate = null): float
    {
        // Get product costs from completed orders
        $productCosts = $this->getProductCosts($startDate, $endDate);


        // Get manual expenses
        $manualExpenses = Expense::getTotalExpenses($startDate, $endDate);

        return $productCosts + $manualExpenses;
    }

    public function getTotalRevenue($startDate = null, $endDate = null): float
    {
        try {
            // Get sales revenue (same as total sales count)
            $salesRevenue = $this->getTotalSalesCount($startDate, $endDate);

            // Get manual income
            $manualIncome = Income::getTotalIncome($startDate, $endDate);

            return $salesRevenue + $manualIncome;
        } catch (\Exception $e) {
            Log::error('Error calculating total revenue: ' . $e->getMessage());
            return 0;
        }
    }

    public function getNetProfit($startDate = null, $endDate = null): float
    {
        $revenue = $this->getTotalRevenue($startDate, $endDate);
        $expenses = $this->getTotalExpenses($startDate, $endDate);

        return $revenue - $expenses;
    }

    protected function getProductCosts($startDate = null, $endDate = null): float
    {
        $query = OrderProduct::query()
            ->join('ec_orders', 'ec_orders.id', '=', 'ec_order_product.order_id')
            ->join('ec_products', 'ec_products.id', '=', 'ec_order_product.product_id')
            ->where('ec_orders.status', OrderStatusEnum::COMPLETED)
            ->where('ec_orders.is_finished', true);

        if ($startDate && $endDate) {
            $startDateStr = is_string($startDate) ? $startDate : $startDate->format('Y-m-d');
            $endDateStr = is_string($endDate) ? $endDate : $endDate->format('Y-m-d');
            $query->whereDate('ec_orders.created_at', '>=', $startDateStr)
                  ->whereDate('ec_orders.created_at', '<=', $endDateStr);
        }

        return $query->sum(DB::raw('COALESCE(ec_products.cost_per_item, 0) * ec_order_product.qty')) ?? 0;
    }

    public function getDashboardData($startDate = null, $endDate = null): array
    {
        // Debug: Log the data for troubleshooting
        $totalSales = $this->getTotalSalesCount($startDate, $endDate);
        $totalRevenue = $this->getTotalRevenue($startDate, $endDate);

        Log::info('Expense Calculations Debug', [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'total_sales' => $totalSales,
            'total_revenue' => $totalRevenue,
            'payment_plugin_active' => is_plugin_active('payment'),
            'orders_count' => Order::where('is_finished', true)->count(),
        ]);

        return [
            'total_sales_count' => $totalSales,
            'total_sale_top_items' => $this->getTotalSaleOfTopSellingItems($startDate, $endDate),
            'total_expenses' => $this->getTotalExpenses($startDate, $endDate),
            'total_revenue' => $totalRevenue,
            'net_profit' => $this->getNetProfit($startDate, $endDate),
        ];
    }

    public function getFinancialChartData($startDate = null, $endDate = null): array
    {
        try {
            // Get daily financial data for chart
            $salesData = $this->getDailySalesData($startDate, $endDate);
            $expenseData = $this->getDailyExpenseData($startDate, $endDate);
            $revenueData = $this->getDailyRevenueData($startDate, $endDate);

            // Get all unique dates and sort them
            $allDates = array_unique(array_merge(
                array_keys($salesData),
                array_keys($expenseData),
                array_keys($revenueData)
            ));
            sort($allDates);

            // If no data or same start/end date, ensure we have at least the requested date range
            if (empty($allDates) || ($startDate && $endDate && $startDate === $endDate)) {
                $startDateStr = $startDate ? $startDate : date('Y-m-d');
                $endDateStr = $endDate ? $endDate : date('Y-m-d');

                // If same date, create a small range for better chart display
                if ($startDateStr === $endDateStr) {
                    $allDates = [$startDateStr];
                } else {
                    // Create date range
                    $period = new \DatePeriod(
                        new \DateTime($startDateStr),
                        new \DateInterval('P1D'),
                        (new \DateTime($endDateStr))->modify('+1 day')
                    );
                    $allDates = [];
                    foreach ($period as $date) {
                        $allDates[] = $date->format('Y-m-d');
                    }
                }
            }

            // Ensure all data arrays have the same dates with 0 values for missing dates
            $alignedSalesData = [];
            $alignedExpenseData = [];
            $alignedRevenueData = [];

            foreach ($allDates as $date) {
                $alignedSalesData[] = (float) ($salesData[$date] ?? 0);
                $alignedExpenseData[] = (float) ($expenseData[$date] ?? 0);
                $alignedRevenueData[] = (float) ($revenueData[$date] ?? 0);
            }

            // Prepare chart series with aligned data
            $series = [
                [
                    'name' => trans('plugins/expense-calculations::expense-calculations.total_sales_count'),
                    'data' => $alignedSalesData,
                ],
                [
                    'name' => trans('plugins/expense-calculations::expense-calculations.total_expenses'),
                    'data' => $alignedExpenseData,
                ],
                [
                    'name' => trans('plugins/expense-calculations::expense-calculations.total_revenue'),
                    'data' => $alignedRevenueData,
                ],
            ];

            $colors = ['#007bff', '#dc3545', '#28a745'];

            return [
                'series' => $series,
                'colors' => $colors,
                'dates' => $allDates,
                'earningSales' => [
                    [
                        'color' => '#007bff',
                        'text' => trans('plugins/expense-calculations::expense-calculations.total_sales_count'),
                    ],
                    [
                        'color' => '#dc3545',
                        'text' => trans('plugins/expense-calculations::expense-calculations.total_expenses'),
                    ],
                    [
                        'color' => '#28a745',
                        'text' => trans('plugins/expense-calculations::expense-calculations.total_revenue'),
                    ],
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error getting financial chart data: ' . $e->getMessage());
            return [
                'series' => [],
                'colors' => [],
                'dates' => [],
                'earningSales' => [],
            ];
        }
    }

    public function getRevenueBreakdownData($startDate = null, $endDate = null): array
    {
        try {
            $salesRevenue = $this->getTotalSalesCount($startDate, $endDate);
            $manualIncome = Income::getTotalIncome($startDate, $endDate);
            $productCosts = $this->getProductCosts($startDate, $endDate);
            $manualExpenses = Expense::getTotalExpenses($startDate, $endDate);

            // Ensure all values are valid numbers (not NaN or null)
            $salesRevenue = is_numeric($salesRevenue) ? (float) $salesRevenue : 0;
            $manualIncome = is_numeric($manualIncome) ? (float) $manualIncome : 0;
            $productCosts = is_numeric($productCosts) ? (float) $productCosts : 0;
            $manualExpenses = is_numeric($manualExpenses) ? (float) $manualExpenses : 0;

            return [
                [
                    'label' => trans('plugins/expense-calculations::expense-calculations.sales_revenue'),
                    'value' => $salesRevenue,
                    'color' => '#007bff',
                    'status' => true,
                ],
                [
                    'label' => trans('plugins/expense-calculations::expense-calculations.manual_income'),
                    'value' => $manualIncome,
                    'color' => '#17a2b8',
                    'status' => false,
                ],
                [
                    'label' => trans('plugins/expense-calculations::expense-calculations.product_costs'),
                    'value' => $productCosts,
                    'color' => '#dc3545',
                    'status' => false,
                ],
                [
                    'label' => trans('plugins/expense-calculations::expense-calculations.manual_expenses'),
                    'value' => $manualExpenses,
                    'color' => '#fd7e14',
                    'status' => false,
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error getting revenue breakdown data: ' . $e->getMessage());
            return [];
        }
    }

    public function getDailySalesData($startDate, $endDate): array
    {
        try {
            // Ensure dates are in proper format
            $startDateStr = is_string($startDate) ? $startDate : $startDate->format('Y-m-d');
            $endDateStr = is_string($endDate) ? $endDate : $endDate->format('Y-m-d');

            if (is_plugin_active('payment')) {
                $query = Order::query()
                    ->select([
                        DB::raw('DATE(payments.created_at) as date'),
                        DB::raw('SUM(COALESCE(payments.amount, 0) - COALESCE(payments.refunded_amount, 0)) as total'),
                    ])
                    ->join('payments', 'payments.id', '=', 'ec_orders.payment_id')
                    ->where('payments.status', PaymentStatusEnum::COMPLETED)
                    ->where('ec_orders.is_finished', true)
                    ->whereDate('payments.created_at', '>=', $startDateStr)
                    ->whereDate('payments.created_at', '<=', $endDateStr)
                    ->groupBy('date')
                    ->orderBy('date');
            } else {
                $query = Order::query()
                    ->select([
                        DB::raw('DATE(created_at) as date'),
                        DB::raw('SUM(amount) as total'),
                    ])
                    ->where('status', OrderStatusEnum::COMPLETED)
                    ->where('is_finished', true)
                    ->whereDate('created_at', '>=', $startDateStr)
                    ->whereDate('created_at', '<=', $endDateStr)
                    ->groupBy('date')
                    ->orderBy('date');
            }

            $result = $query->pluck('total', 'date')->toArray();

            // Ensure all values are valid numbers
            foreach ($result as $date => $value) {
                $result[$date] = is_numeric($value) ? (float) $value : 0;
            }

            // If no data found and we have specific dates, ensure we return at least the date range with 0 values
            if (empty($result) && $startDate && $endDate) {
                $startDateStr = is_string($startDate) ? $startDate : $startDate->format('Y-m-d');
                $endDateStr = is_string($endDate) ? $endDate : $endDate->format('Y-m-d');

                if ($startDateStr === $endDateStr) {
                    // Single date
                    $result[$startDateStr] = 0;
                } else {
                    // Date range - fill with zeros
                    $period = new \DatePeriod(
                        new \DateTime($startDateStr),
                        new \DateInterval('P1D'),
                        (new \DateTime($endDateStr))->modify('+1 day')
                    );
                    foreach ($period as $date) {
                        $dateStr = $date->format('Y-m-d');
                        if (!isset($result[$dateStr])) {
                            $result[$dateStr] = 0;
                        }
                    }
                }
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error getting daily sales data: ' . $e->getMessage());
            return [];
        }
    }

    public function getDailyExpenseData($startDate, $endDate): array
    {
        try {
            // Ensure dates are in proper format
            $startDateStr = is_string($startDate) ? $startDate : $startDate->format('Y-m-d');
            $endDateStr = is_string($endDate) ? $endDate : $endDate->format('Y-m-d');

            $manualExpenses = Expense::query()
                ->select([
                    DB::raw('DATE(expense_date) as date'),
                    DB::raw('SUM(amount) as total'),
                ])
                ->active()
                ->whereDate('expense_date', '>=', $startDateStr)
                ->whereDate('expense_date', '<=', $endDateStr)
                ->groupBy('date')
                ->orderBy('date')
                ->pluck('total', 'date')
                ->toArray();

            // Get product costs by order date
            $productCosts = OrderProduct::query()
                ->select([
                    DB::raw('DATE(ec_orders.created_at) as date'),
                    DB::raw('SUM(COALESCE(ec_products.cost_per_item, 0) * ec_order_product.qty) as total'),
                ])
                ->join('ec_orders', 'ec_orders.id', '=', 'ec_order_product.order_id')
                ->join('ec_products', 'ec_products.id', '=', 'ec_order_product.product_id')
                ->where('ec_orders.status', OrderStatusEnum::COMPLETED)
                ->where('ec_orders.is_finished', true)
                ->whereDate('ec_orders.created_at', '>=', $startDateStr)
                ->whereDate('ec_orders.created_at', '<=', $endDateStr)
                ->groupBy('date')
                ->orderBy('date')
                ->pluck('total', 'date')
                ->toArray();

            // Merge manual expenses and product costs
            $allDates = array_unique(array_merge(array_keys($manualExpenses), array_keys($productCosts)));
            $result = [];

            foreach ($allDates as $date) {
                $manualExpense = is_numeric($manualExpenses[$date] ?? 0) ? (float) ($manualExpenses[$date] ?? 0) : 0;
                $productCost = is_numeric($productCosts[$date] ?? 0) ? (float) ($productCosts[$date] ?? 0) : 0;
                $result[$date] = $manualExpense + $productCost;
            }

            // If no data found and we have specific dates, ensure we return at least the date range with 0 values
            if (empty($result) && $startDate && $endDate) {
                $startDateStr = is_string($startDate) ? $startDate : $startDate->format('Y-m-d');
                $endDateStr = is_string($endDate) ? $endDate : $endDate->format('Y-m-d');

                if ($startDateStr === $endDateStr) {
                    // Single date
                    $result[$startDateStr] = 0;
                } else {
                    // Date range - fill with zeros
                    $period = new \DatePeriod(
                        new \DateTime($startDateStr),
                        new \DateInterval('P1D'),
                        (new \DateTime($endDateStr))->modify('+1 day')
                    );
                    foreach ($period as $date) {
                        $dateStr = $date->format('Y-m-d');
                        if (!isset($result[$dateStr])) {
                            $result[$dateStr] = 0;
                        }
                    }
                }
            }

            ksort($result);
            return $result;
        } catch (\Exception $e) {
            Log::error('Error getting daily expense data: ' . $e->getMessage());
            return [];
        }
    }

    public function getDailyRevenueData($startDate, $endDate): array
    {
        try {
            // Ensure dates are in proper format
            $startDateStr = is_string($startDate) ? $startDate : $startDate->format('Y-m-d');
            $endDateStr = is_string($endDate) ? $endDate : $endDate->format('Y-m-d');

            $salesData = $this->getDailySalesData($startDate, $endDate);

            $manualIncome = Income::query()
                ->select([
                    DB::raw('DATE(income_date) as date'),
                    DB::raw('SUM(amount) as total'),
                ])
                ->active()
                ->whereDate('income_date', '>=', $startDateStr)
                ->whereDate('income_date', '<=', $endDateStr)
                ->groupBy('date')
                ->orderBy('date')
                ->pluck('total', 'date')
                ->toArray();

            // Merge sales and manual income
            $allDates = array_unique(array_merge(array_keys($salesData), array_keys($manualIncome)));
            $result = [];

            foreach ($allDates as $date) {
                $salesAmount = is_numeric($salesData[$date] ?? 0) ? (float) ($salesData[$date] ?? 0) : 0;
                $incomeAmount = is_numeric($manualIncome[$date] ?? 0) ? (float) ($manualIncome[$date] ?? 0) : 0;
                $result[$date] = $salesAmount + $incomeAmount;
            }

            // If no data found and we have specific dates, ensure we return at least the date range with 0 values
            if (empty($result) && $startDate && $endDate) {
                $startDateStr = is_string($startDate) ? $startDate : $startDate->format('Y-m-d');
                $endDateStr = is_string($endDate) ? $endDate : $endDate->format('Y-m-d');

                if ($startDateStr === $endDateStr) {
                    // Single date
                    $result[$startDateStr] = 0;
                } else {
                    // Date range - fill with zeros
                    $period = new \DatePeriod(
                        new \DateTime($startDateStr),
                        new \DateInterval('P1D'),
                        (new \DateTime($endDateStr))->modify('+1 day')
                    );
                    foreach ($period as $date) {
                        $dateStr = $date->format('Y-m-d');
                        if (!isset($result[$dateStr])) {
                            $result[$dateStr] = 0;
                        }
                    }
                }
            }

            ksort($result);
            return $result;
        } catch (\Exception $e) {
            Log::error('Error getting daily revenue data: ' . $e->getMessage());
            return [];
        }
    }
}
