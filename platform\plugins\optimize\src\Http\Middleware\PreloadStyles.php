<?php

namespace Shaqi\AdvancedOptimize\Http\Middleware;

use DOMDocument;
use DOMXPath;
use Bo<PERSON>ble\Optimize\Http\Middleware\PageSpeed;

class PreloadStyles extends PageSpeed
{
    public function apply(string $buffer): string
    {
        libxml_use_internal_errors(true);
        $dom = new DOMDocument();
        $dom->loadHTML($buffer, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $xpath = new DOMXPath($dom);

        foreach ($xpath->query('//link[@rel="stylesheet"]') as $link) {

            // Check if the link has the data-pagespeed-no-preload attribute
            if ($link->hasAttribute('data-pagespeed-no-preload')) {
                // Skip the link with data-skip-preload
                continue;
            }
            // Insert a copy of link inside the <noscript>
            $noscript = $dom->createElement('noscript');
            $noscript->appendChild($link->cloneNode(true));
            $link->parentNode->insertBefore($noscript, $link->nextSibling);

            // Modify the link attributes
            $link->setAttribute('rel', 'preload');
            $link->setAttribute('as', 'style');
            $link->setAttribute('onload', "this.onload=null;this.rel='stylesheet'");

    }

        return $dom->saveHTML();
    }
}
