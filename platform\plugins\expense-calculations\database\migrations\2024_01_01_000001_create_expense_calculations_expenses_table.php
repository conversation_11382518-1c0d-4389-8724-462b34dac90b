<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('ec_expenses', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->decimal('amount', 15, 2);
            $table->string('category')->nullable();
            $table->date('expense_date');
            $table->string('reference_number')->nullable();
            $table->json('attachments')->nullable();
            $table->string('status', 60)->default('active');
            $table->timestamps();
            
            $table->index(['expense_date', 'status']);
            $table->index('category');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ec_expenses');
    }
};
