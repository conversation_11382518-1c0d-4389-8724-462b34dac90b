<?php

return [
    [
        'name' => 'Expense Calculations',
        'flag' => 'expense-calculations.dashboard',
        'parent_flag' => 'core.cms',
    ],
    [
        'name' => 'Dashboard',
        'flag' => 'expense-calculations.dashboard',
        'parent_flag' => 'expense-calculations.dashboard',
    ],
    [
        'name' => 'Expenses',
        'flag' => 'expense-calculations.expenses.index',
        'parent_flag' => 'expense-calculations.dashboard',
    ],
    [
        'name' => 'Create Expense',
        'flag' => 'expense-calculations.expenses.create',
        'parent_flag' => 'expense-calculations.expenses.index',
    ],
    [
        'name' => 'Edit Expense',
        'flag' => 'expense-calculations.expenses.edit',
        'parent_flag' => 'expense-calculations.expenses.index',
    ],
    [
        'name' => 'Delete Expense',
        'flag' => 'expense-calculations.expenses.destroy',
        'parent_flag' => 'expense-calculations.expenses.index',
    ],
    [
        'name' => 'Income',
        'flag' => 'expense-calculations.income.index',
        'parent_flag' => 'expense-calculations.dashboard',
    ],
    [
        'name' => 'Create Income',
        'flag' => 'expense-calculations.income.create',
        'parent_flag' => 'expense-calculations.income.index',
    ],
    [
        'name' => 'Edit Income',
        'flag' => 'expense-calculations.income.edit',
        'parent_flag' => 'expense-calculations.income.index',
    ],
    [
        'name' => 'Delete Income',
        'flag' => 'expense-calculations.income.destroy',
        'parent_flag' => 'expense-calculations.income.index',
    ],
];
