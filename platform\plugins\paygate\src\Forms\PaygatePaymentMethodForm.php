<?php

namespace Botble\Paygate\Forms;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\TextField;
use Botble\Payment\Concerns\Forms\HasAvailableCountriesField;
use Botble\Payment\Forms\PaymentMethodForm;

class PaygatePaymentMethodForm extends PaymentMethodForm
{
    use HasAvailableCountriesField;

    public function setup(): void
    {
        parent::setup();

        $this
            ->paymentId(PAYGATE_PAYMENT_METHOD_NAME)
            ->paymentName('Paygate')
            ->paymentDescription(__('Customer can pay using cryptocurrency via :name', ['name' => 'Paygate']))
            ->paymentLogo(url('vendor/core/plugins/paygate/images/paygate.png'))
            ->paymentFeeField(PAYGATE_PAYMENT_METHOD_NAME)
            ->paymentUrl('https://paygate.to')
            ->paymentInstructions(view('plugins/paygate::instructions')->render())
            ->add(
                sprintf('payment_%s_wallet_address', PAYGATE_PAYMENT_METHOD_NAME),
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Wallet Address'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('wallet_address', PAYGATE_PAYMENT_METHOD_NAME))
                    ->placeholder(__('Enter your wallet address'))
                    ->attributes(['data-counter' => 400])
                    ->wrapperAttributes(['class' => 'col-12'])
            )
            ->addAvailableCountriesField(PAYGATE_PAYMENT_METHOD_NAME);
    }
}
