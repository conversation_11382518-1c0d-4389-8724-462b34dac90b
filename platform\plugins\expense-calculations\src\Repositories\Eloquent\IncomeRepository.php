<?php

namespace Botble\ExpenseCalculations\Repositories\Eloquent;

use Botble\ExpenseCalculations\Repositories\Interfaces\IncomeInterface;
use Bo<PERSON>ble\Support\Repositories\Eloquent\RepositoriesAbstract;

class IncomeRepository extends RepositoriesAbstract implements IncomeInterface
{
    public function getTotalIncome($startDate = null, $endDate = null)
    {
        return $this->model->getTotalIncome($startDate, $endDate);
    }
    
    public function getIncomeBySource($startDate = null, $endDate = null)
    {
        return $this->model->getIncomeBySource($startDate, $endDate);
    }
    
    public function getIncomeByDateRange($startDate, $endDate)
    {
        return $this->model->active()->byDateRange($startDate, $endDate)->get();
    }
}
